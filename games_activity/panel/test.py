import query_builder as qb
import asyncio
from query_builder import BIGINT

ps_db = qb.db(db_name='ps')
ps_public = qb.Schema('public', db_name='ps')

async def get_new_player_base_a():
    query = (
        qb.Query(ps_public.new_player_base_a)
        .select(ps_public.new_player_base_a.c.account_id)
    )
    results = await query.fetchall()
    results = [r['account_id'] for r in results]
    return results

async def read_active_users():
    active_users = set()
    try:
        with open('panel/accounts/ps-active-users.tsv', 'r') as f:
            first_line = f.readline().strip()
            if first_line != "user_id":
                active_users.add(int(first_line))
            for line in f:
                user_id = line.strip()
                if user_id:
                    active_users.add(int(user_id))
    except FileNotFoundError:
        print("Active users file not found. Creating an empty set.")
    return list(active_users)

async def populate_new_panel_table():

    print("Loading new player base...")
    new_player_base = await get_new_player_base_a()
    print(f"Loaded {len(new_player_base)} new players")

    print("Loading active player base...")
    active_player_base = await read_active_users()
    print(f"Loaded {len(active_player_base)} active players")

    # Combine both lists and remove duplicates
    all_account_ids = set(new_player_base + active_player_base)
    print(f"Total unique account IDs: {len(all_account_ids)}")



    # Prepare data for insertion
    records_to_insert = []
    groupnum = 0
    count = 0
    # groupnum is the floor of count / 100
    for account_id in all_account_ids:
        groupnum = count // 100
        records_to_insert.append({
            'account_id': account_id,
            'id': count,
            'groupnum': groupnum
        })
        count += 1

    print(f"Inserting {len(records_to_insert)} records into public.new_panel...")

    if (await qb.table_exists('public', 'new_panel_users', db_name='ps')):
            await ps_public.new_panel_users.drop()

    temp_table: qb.Table = qb.create_table(
        schema='public',
        name='new_panel_users',
        columns =[qb.TableColumn('account_id', qb.BIGINT), qb.TableColumn('id', qb.BIGINT), qb.TableColumn('groupnum', qb.INTEGER)],
        constraints=[qb.UniqueConstraint('id')],
        db_name='ps'
    )

    async with temp_table.copy_in() as copy_in:
        for u in records_to_insert:
            await copy_in.write_row([u['account_id'], u['id'], u['groupnum']])

    print("Successfully populated public.new_panel table!")

async def main():
    # Uncomment the line below to populate the new_panel table
    await populate_new_panel_table()

if __name__ == "__main__":
    asyncio.run(main())
