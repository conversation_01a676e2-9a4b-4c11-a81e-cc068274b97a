import query_builder as qb
import asyncio
from query_builder import BIGINT

ps_db = qb.db(db_name='ps')
ps_public = qb.Schema('public', db_name='ps')

async def load_active_users_from_file():
    active_users = set()
    try:
        with open('panel/accounts/ps-active-users.tsv', 'r') as f:
            first_line = f.readline().strip()
            if first_line != "user_id":
                active_users.add(int(first_line))
            for line in f:
                user_id = line.strip()
                if user_id:
                    active_users.add(int(user_id))
        print(f"Read {len(active_users)} active users from file")
    except FileNotFoundError:
        print("Active users file not found. Creating an empty set.")
    return active_users

async def load_inactive_users_from_file():
    inactive_users = set()
    try:
        with open('panel/accounts/ps-inactive-users.tsv', 'r') as f:
            first_line = f.readline().strip()
            if first_line != "user_id":
                inactive_users.add(int(first_line))
            for line in f:
                user_id = line.strip()
                if user_id:
                    inactive_users.add(int(user_id))
        print(f"Read {len(inactive_users)} inactive users from file")
    except FileNotFoundError:
        print("Inactive users file not found. Creating an empty set.")
    return inactive_users

async def get_country_id_tally(users):
    if (await qb.table_exists('public', 'tmp_users', db_name='ps')):
        await ps_public.tmp_users.drop()
    
    temp_table: qb.Table = qb.create_table(
        schema='public',
        name='tmp_users',
        columns =[qb.TableColumn('account_id', qb.BIGINT)],
        constraints=[qb.UniqueConstraint('account_id')],
        db_name='ps'
    )

    async with temp_table.copy_in() as copy_in:
        for u in users:
            await copy_in.write_row([u])

    query = (
        qb.select(ps_public.players.c.country_id, qb.func.count().label('count'))
        .join(temp_table, ps_public.players.c.account_id == temp_table.c.account_id)
        .group_by(ps_public.players.c.country_id)
        .order_by(qb.desc('count'))
    )

    results = await query.q.fetchall()
    await temp_table.drop()

    # changes country_id to country_name found in public.countries
    country_query = (
        qb.select(ps_public.countries.c.country_name, ps_public.countries.c.country_id)
    )
    country_results = await country_query.q.fetchall()
    country_dict = {r['country_id']: r['country_name'] for r in country_results}
    for r in results:
        r['country_name'] = country_dict[r['country_id']]
        del r['country_id']
    
    return results

async def save_tally(tally, file_path):
    with open(file_path, 'w') as f:
        f.write("country_name,count\n")
        for r in tally:
            f.write(f"{r['country_name']},{r['count']}\n")

async def main():
    active_tally = list(await load_active_users_from_file())
    tally = list(await get_country_id_tally(active_tally))
    await save_tally(tally, 'panel/accounts/ps-active-country-tally.csv')
    print("done active")

    inactive_tally = list(await load_inactive_users_from_file())
    tally = list(await get_country_id_tally(inactive_tally))
    await save_tally(tally, 'panel/accounts/ps-inactive-country-tally.csv')
    print("done inactive")

if __name__ == "__main__":
    asyncio.run(main())
