from panel.playstation.get_unmonitored_accounts import get_tokens
import asyncio

from panel.playstation.helpers.auth_edit import Authenticator
from panel.playstation.helpers.async_request_builder import AsyncRequestBuilder
from panel.playstation.helpers.proxy_handler_client import ProxySession
import query_builder as qb
import random
from datetime import datetime, timedelta, timezone

from logging_utils import init_logger, logging

ps_db = qb.db(db_name='ps')
ps_public = qb.Schema('public', db_name='ps')
ps_metrics = qb.Schema('metrics', db_name='ps')


NUMBER_OF_TOKENS = 1

logger = init_logger()

async def get_trophy_summary(builder: AsyncRequestBuilder, account_id: int, offset=0, sessions=None,
                             retries=3):
    base_uri = f'https://m.np.playstation.com/api/trophy/v1/users/{account_id}/trophyTitles'
    params = {
        'limit': 800,
        'offset': offset
    }
    retries = 0
    while True:
        session = random.choice(sessions)
        try:
            response = await builder.get(url=base_uri, session=session, params=params)
            return response
        except Exception as e:
            print(e)
            retries += 1
            if retries > retries:
                return None
            await asyncio.sleep(1)

async def get_trophy_groups(builder: AsyncRequestBuilder, trophy_title_id: str,
                            np_service_name: str, sessions,  account_id: int = None, trophy_data=None):
    if account_id is None:
        base_uri = f'https://m.np.playstation.com/api/trophy/v1/npCommunicationIds/{trophy_title_id}/' \
                   f'trophyGroups?npServiceName={np_service_name}'
    else:
        base_uri = f'https://m.np.playstation.com/api/trophy/v1/users/{account_id}/npCommunicationIds' \
                   f'/{trophy_title_id}/trophyGroups?npServiceName={np_service_name}'
    while True:
        session = random.choice(sessions)
        try:
            response = await builder.get(url=base_uri, session=session)
            if trophy_data is not None:
                trophy_data['trophy_groups'] = [group['trophyGroupId'] for group in response['trophyGroups']]
                return
            else:
                return response['trophyGroups']
        except Exception as e:
            print(e)
           
async def get_player_trophy_list(builder: AsyncRequestBuilder, account_id: int, trophy_title_id: str,
                                 np_service_name: str, sessions,  trophy_group: str = None, trophy_data=None):
    base_uri = f'https://m.np.playstation.com/api/trophy/v1/users/{account_id}/npCommunicationIds/{trophy_title_id}/' \
               f'trophyGroups/{trophy_group}/trophies'
    params = {
        'npServiceName': np_service_name,
        'limit': 100000,
        'offset': 0
    }
    while True:
        session = random.choice(sessions)
        try:
            response = await builder.get(url=base_uri, session=session, params=params)
            if trophy_data is not None:
                trophy_data['trophies'] += response['trophies']
                return
            else:
                return response
        except Exception as e:
            print(e)

async def get_all_trophies(builder: AsyncRequestBuilder, account_id: int, sessions):
    response = await get_trophy_summary(builder, account_id, sessions=sessions)
    if response is None:
        return
    total = response['totalItemCount']
    with_groups = []
    all_trophy_titles = []
    offset = 0
    while offset < total:
        response = await get_trophy_summary(builder, account_id, offset, sessions=sessions)
        if response is None:
            break
        for trophy in response['trophyTitles']:
            if trophy['hasTrophyGroups']:
                with_groups.append(trophy)
            else:
                all_trophy_titles.append(trophy)
        offset += 800
    for trophy in all_trophy_titles:
        trophy['trophy_groups'] = ['default']

    with_groups = [with_groups[i:i + 200] for i in range(0, len(with_groups), 200)]
    for trophies in with_groups:
        await asyncio.gather(*[get_trophy_groups(builder, account_id=account_id,
                                                 trophy_title_id=trophy['npCommunicationId'],
                                                 np_service_name=trophy['npServiceName'],
                                                 trophy_data=trophy,
                                                 sessions=sessions) for trophy in trophies])
        all_trophy_titles += trophies
    tasks = []
    for trophy in all_trophy_titles:
        trophy['trophies'] = []
        tasks += [get_player_trophy_list(builder, account_id, trophy['npCommunicationId'],
                                         trophy['npServiceName'], trophy_group=group,
                                         trophy_data=trophy, sessions=sessions) for group in trophy['trophy_groups']]
    tasks = [tasks[i:i + 100] for i in range(0, len(tasks), 100)]
    for task_group in tasks:
        await asyncio.gather(*task_group)
    return all_trophy_titles

async def get_proxy_request(number=20):
    import requests
    import random
    url = "https://proxy.ampere-analytics.com/proxy_api/proxies/proxy?proxy_type=datacenter&proxy_type=vpn&country_code=MX&country_code=US"

    proxy_list = []
    for _ in range(number):
        response = requests.get(url)

        
        if response.status_code == 200:
            included_providers = ['rayobyte']
            proxies = response.json()  # or .text if it’s plain text
            proxies = [p for p in proxies if p['provider']['name'] in included_providers]
            while True:
                prox = random.choice(proxies)
                p = prox['proxy']
                cred = prox['provider']['credentials']
                if cred:
                    proxy = {'ip': p['ip'], 'port': p['port'], 'username': cred['username'], 'password': cred['password']}
                    proxy_list.append(proxy)
                    break
        else:
            print(f"Error {response.status_code}: {response.text}")
    return proxy_list

async def get_proxies():
    all_proxies_sessions = []
    proxies = await get_proxy_request()
    for proxy in proxies:
        proxy_data = {
            "proxy": {
                "protocol": "http",
                "ip": proxy['ip'],
                "port": proxy['port'],
                "ssl": False,
                "unique_proxy_id": "custom-proxy-1"
            },
            "provider": {
                "credentials": {
                    "username": proxy['username'],
                    "password": proxy['password']
                },
                "name": "custom"
            }
        }

        proxy_session = await ProxySession.async_init_from_data(
            logger=logging.getLogger(),
            proxy_data=proxy_data,
            do_not_update=True
        )
        all_proxies_sessions.append(proxy_session)
    
    return all_proxies_sessions

async def run():
    try:
        proxy_pool = await get_proxies()

        # Get all available tokens
        tokens = await get_tokens(number_of_tokens=NUMBER_OF_TOKENS)
        print(f"Got {len(tokens)} tokens")

        # Create authenticators and builders for each token
        builders = []
        for i, token in enumerate(tokens):
            try:
                authenticator = await Authenticator.async_init(npsso_token=token, proxy_pool=proxy_pool, logger=logger)
                if authenticator.success:
                    builder = AsyncRequestBuilder(authenticator)
                    builders.append(builder)
                    print(f"Successfully created builder {i+1}")
                else:
                    print(f"Failed to authenticate token {i+1}")
            except Exception as e:
                print(f"Error creating authenticator {i+1}: {e}")

        print(f"Created {len(builders)} working builders")

        if not builders:
            print("No working builders available!")
            return
        
        all_trophy_titles = await get_all_trophies(builders[0], 104192958940080447, proxy_pool)
        print(all_trophy_titles)

    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Close all proxy sessions
        if 'proxy_pool' in locals() and proxy_pool:
            for session in proxy_pool:
                try:
                    await session.close()
                except Exception as e:
                    print(f"Error closing session: {e}")

async def main():
    import time
    time_0 = time.time()
    print("Starting")

    
    await run()
    
    time_1 = time.time()
    print(f"Time: {time_1 - time_0}")

if __name__ == "__main__":
    asyncio.run(main())








