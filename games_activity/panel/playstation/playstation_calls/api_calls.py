"""
A clean centralized version of all PlayStation API calls.
Combines functionality from friends.py, precence.py, recent_games.py, and trophies.py
"""

from panel.playstation.get_unmonitored_accounts import get_tokens
import asyncio

from panel.playstation.helpers.auth_edit import Authenticator
from panel.playstation.helpers.async_request_builder import AsyncRequestBuilder
from panel.playstation.helpers.proxy_handler_client import ProxySession
import query_builder as qb
import random

from logging_utils import init_logger, logging

logger = init_logger()

ps_db = qb.db(db_name='ps')
ps_public = qb.Schema('public', db_name='ps')
ps_metrics = qb.Schema('metrics', db_name='ps')

NUMBER_OF_TOKENS = 1

# ============================================================================
# PROXY MANAGEMENT
# ============================================================================

async def get_proxy_request(number=20):
    """Get proxy list from the proxy API."""
    import requests
    import random
    url = "https://proxy.ampere-analytics.com/proxy_api/proxies/proxy?proxy_type=datacenter&proxy_type=vpn&country_code=MX&country_code=US"

    proxy_list = []
    for _ in range(number):
        response = requests.get(url)

        if response.status_code == 200:
            included_providers = ['rayobyte']
            proxies = response.json()  # or .text if it's plain text
            proxies = [p for p in proxies if p['provider']['name'] in included_providers]
            while True:
                prox = random.choice(proxies)
                p = prox['proxy']
                cred = prox['provider']['credentials']
                if cred:
                    proxy = {'ip': p['ip'], 'port': p['port'], 'username': cred['username'], 'password': cred['password']}
                    proxy_list.append(proxy)
                    break
        else:
            print(f"Error {response.status_code}: {response.text}")
    return proxy_list


async def get_proxies():
    """Initialize proxy sessions from proxy list."""
    all_proxies_sessions = []
    proxies = await get_proxy_request()
    for proxy in proxies:
        proxy_data = {
            "proxy": {
                "protocol": "http",
                "ip": proxy['ip'],
                "port": proxy['port'],
                "ssl": False,
                "unique_proxy_id": "custom-proxy-1"
            },
            "provider": {
                "credentials": {
                    "username": proxy['username'],
                    "password": proxy['password']
                },
                "name": "custom"
            }
        }

        proxy_session = await ProxySession.async_init_from_data(
            logger=logging.getLogger(),
            proxy_data=proxy_data,
            do_not_update=True
        )
        all_proxies_sessions.append(proxy_session)

    return all_proxies_sessions


# ============================================================================
# AUTHENTICATION AND BUILDER SETUP
# ============================================================================

async def setup_builders(number_of_tokens=NUMBER_OF_TOKENS, proxy_pool=None):
    """Setup authenticated request builders."""
    if proxy_pool is None:
        proxy_pool = await get_proxies()

    # Get all available tokens
    tokens = await get_tokens(number_of_tokens=number_of_tokens)
    print(f"Got {len(tokens)} tokens")

    # Create authenticators and builders for each token
    builders = []
    for i, token in enumerate(tokens):
        try:
            authenticator = await Authenticator.async_init(npsso_token=token, proxy_pool=proxy_pool, logger=logger)
            if authenticator.success:
                builder = AsyncRequestBuilder(authenticator)
                builders.append(builder)
                print(f"Successfully created builder {i+1}")
            else:
                print(f"Failed to authenticate token {i+1}")
        except Exception as e:
            print(f"Error creating authenticator {i+1}: {e}")

    print(f"Created {len(builders)} working builders")
    return builders, proxy_pool


# ============================================================================
# FRIENDS API
# ============================================================================

async def get_friends(builder: AsyncRequestBuilder, account_id, sessions=None, retries=3):
    """Get friends list for a PlayStation account."""
    base_uri = f"https://m.np.playstation.com/api/userProfile/v1/internal/users/{account_id}/friends"
    params = {}

    return await get_response(builder, base_uri, params, sessions, retries)


# ============================================================================
# PRESENCE API
# ============================================================================

async def get_basic_presences(builder: AsyncRequestBuilder, account_ids, sessions=None, retries=3):
    """Get basic presence information for PlayStation accounts."""
    base_uri = f"https://m.np.playstation.com/api/userProfile/v1/internal/users/basicPresences"
    params = {
        'type': 'primary',
        'accountIds': ','.join([str(account_id) for account_id in account_ids])
    }

    return await get_response(builder, base_uri, params, sessions, retries)


# ============================================================================
# GAME LIBRARY API
# ============================================================================

async def get_library(builder: AsyncRequestBuilder, account_id: int, sessions=None, retries=3):
    """Get game library/titles for a PlayStation account."""
    base_uri = f'https://m.np.playstation.com/api/gamelist/v2/users/{account_id}/titles'
    params = {
        'categories': 'ps4_game,ps5_native_game',
        'limit': 200,   # max allowed
        'offset': 0
    }

    all_titles = []

    while True:
        response = await get_response(builder, base_uri, params, sessions, retries)

        items = response.get("titles", [])
        all_titles.extend(items)

        if len(items) < params["limit"]:
            break

        params["offset"] += params["limit"]

    return all_titles


# ============================================================================
# TROPHY API
# ============================================================================

async def get_trophy_summary(builder: AsyncRequestBuilder, account_id: int, offset=0, sessions=None, retries=3):
    """Get trophy summary for a PlayStation account."""
    base_uri = f'https://m.np.playstation.com/api/trophy/v1/users/{account_id}/trophyTitles'
    params = {
        'limit': 800,
        'offset': offset
    }
    return await get_response(builder, base_uri, params, sessions, retries)


async def get_trophy_groups(builder: AsyncRequestBuilder, trophy_title_id: str,
                            np_service_name: str, sessions, account_id: int = None, trophy_data=None, retries=3):
    """Get trophy groups for a specific game."""
    if account_id is None:
        base_uri = f'https://m.np.playstation.com/api/trophy/v1/npCommunicationIds/{trophy_title_id}/' \
                   f'trophyGroups?npServiceName={np_service_name}'
    else:
        base_uri = f'https://m.np.playstation.com/api/trophy/v1/users/{account_id}/npCommunicationIds' \
                   f'/{trophy_title_id}/trophyGroups?npServiceName={np_service_name}'
    params = {}
    return await get_response(builder, base_uri, params, sessions, retries)


async def get_player_trophy_list(builder: AsyncRequestBuilder, account_id: int, trophy_title_id: str,
                                 np_service_name: str, sessions, trophy_group: str = None, trophy_data=None, retries = 3):
    """Get player trophy list for a specific game and trophy group."""
    base_uri = f'https://m.np.playstation.com/api/trophy/v1/users/{account_id}/npCommunicationIds/{trophy_title_id}/' \
               f'trophyGroups/{trophy_group}/trophies'
    params = {
        'npServiceName': np_service_name,
        'limit': 100000,
        'offset': 0
    }
    
    while True:
        try:
            response = await get_response(builder, base_uri, params, sessions, retries)
            if trophy_data is not None:
                trophy_data['trophies'] += response['trophies']
                return
            else:
                return response
        except Exception as e:
            print(e)


async def get_all_trophies(builder: AsyncRequestBuilder, account_id: int, sessions):
    """Get all trophies for a PlayStation account."""
    response = await get_trophy_summary(builder, account_id, sessions=sessions)
    if response is None:
        return
    total = response['totalItemCount']
    with_groups = []
    all_trophy_titles = []
    offset = 0
    while offset < total:
        response = await get_trophy_summary(builder, account_id, offset, sessions=sessions)
        if response is None:
            break
        for trophy in response['trophyTitles']:
            if trophy['hasTrophyGroups']:
                with_groups.append(trophy)
            else:
                all_trophy_titles.append(trophy)
        offset += 800
    for trophy in all_trophy_titles:
        trophy['trophy_groups'] = ['default']

    with_groups = [with_groups[i:i + 200] for i in range(0, len(with_groups), 200)]
    for trophies in with_groups:
        await asyncio.gather(*[get_trophy_groups(builder, account_id=account_id,
                                                 trophy_title_id=trophy['npCommunicationId'],
                                                 np_service_name=trophy['npServiceName'],
                                                 trophy_data=trophy,
                                                 sessions=sessions) for trophy in trophies])
        all_trophy_titles += trophies
    tasks = []
    for trophy in all_trophy_titles:
        trophy['trophies'] = []
        tasks += [await get_player_trophy_list(builder, account_id, trophy['npCommunicationId'],
                                         trophy['npServiceName'], trophy_group=group,
                                         trophy_data=trophy, sessions=sessions) for group in trophy['trophy_groups']]
    tasks = [tasks[i:i + 100] for i in range(0, len(tasks), 100)]
    for task_group in tasks:
        await asyncio.gather(*task_group)
    return all_trophy_titles


# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

async def cleanup_sessions(proxy_pool):
    """Clean up proxy sessions."""
    if proxy_pool:
        for session in proxy_pool:
            try:
                await session.close()
            except Exception as e:
                print(f"Error closing session: {e}")


async def get_response(builder: AsyncRequestBuilder, url: str, params: dict = None, sessions=None, retries=3):
    for attempt in range(retries):
        session = random.choice(sessions)
        try:
            response = await builder.get(url=url, session=session, params=params)
            break
        except Exception:
            if attempt == retries - 1:
                raise
            await asyncio.sleep(1)
    return response

async def parse_duration(duration_str):
    import re
    """
    Convert ISO-8601 duration format (e.g., 'PT23H22M19S') 
    into total seconds and HH:MM:SS readable format.
    """
    if not duration_str:
        return 0, "00:00:00"

    # Regex to capture hours/minutes/seconds
    match = re.match(r"PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?", duration_str)
    if not match:
        return 0, "00:00:00"

    hours = int(match.group(1)) if match.group(1) else 0
    minutes = int(match.group(2)) if match.group(2) else 0
    seconds = int(match.group(3)) if match.group(3) else 0

    total_seconds = hours * 3600 + minutes * 60 + seconds
    readable = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    return total_seconds, readable

async def simplify_and_sort(response):
    simplified = []
    for item in response:
        duration_sec, readable_duration = await parse_duration(item.get("playDuration"))

        simplified.append({
            "name": item.get("name"),
            "playCount": item.get("playCount"),
            "firstPlayed": item.get("firstPlayedDateTime"),
            "lastPlayed": item.get("lastPlayedDateTime"),
            "playDuration": readable_duration,
            "durationSeconds": duration_sec  # used only for sorting
        })

    # Sort by duration descending
    simplified.sort(key=lambda x: x["durationSeconds"], reverse=True)

    # Drop the sorting key before returning
    for entry in simplified:
        entry.pop("durationSeconds")

    return simplified

# ============================================================================
# EXAMPLE USAGE FUNCTIONS
# ============================================================================

async def run_friends_example(account_id=8488126864586279442):
    """Example: Get friends for an account."""
    proxy_pool = None
    try:
        builders, proxy_pool = await setup_builders()
        if not builders:
            print("No working builders available!")
            return

        response = await get_friends(builders[0], account_id, proxy_pool)
        print("Friends response:", response)
        return response
    except Exception as e:
        print(f"Error: {e}")
    finally:
        await cleanup_sessions(proxy_pool)


async def run_presence_example(account_ids=[8488126864586279442]):
    """Example: Get presence for accounts."""
    proxy_pool = None
    try:
        builders, proxy_pool = await setup_builders()
        if not builders:
            print("No working builders available!")
            return

        response = await get_basic_presences(builders[0], account_ids, proxy_pool)
        print("Presence response:", response)
        return response
    except Exception as e:
        print(f"Error: {e}")
    finally:
        await cleanup_sessions(proxy_pool)


async def run_library_example(account_id=*****************):
    """Example: Get game library for an account."""
    proxy_pool = None
    try:
        builders, proxy_pool = await setup_builders()
        if not builders:
            print("No working builders available!")
            return

        response = await get_library(builders[0], account_id, proxy_pool)

        simplified = await simplify_and_sort(response)

        for item in simplified:
            print(item)

        return response
    except Exception as e:
        print(f"Error: {e}")
    finally:
        await cleanup_sessions(proxy_pool)


async def run_trophies_example(account_id=8488126864586279442):
    """Example: Get all trophies for an account."""
    proxy_pool = None
    try:
        builders, proxy_pool = await setup_builders()
        if not builders:
            print("No working builders available!")
            return

        response = await get_all_trophies(builders[0], account_id, proxy_pool)
        print("Trophies response:", response)
        return response
    except Exception as e:
        print(f"Error: {e}")
    finally:
        await cleanup_sessions(proxy_pool)


# ============================================================================
# MAIN EXECUTION
# ============================================================================

async def main():
    """Main function to demonstrate API usage."""
    import time
    time_0 = time.time()
    print("Starting PlayStation API calls...")

    # Uncomment the example you want to run:
    # await run_friends_example()
    # await run_presence_example()
    await run_library_example()
    # await run_trophies_example()

    time_1 = time.time()
    print(f"Time: {time_1 - time_0}")


if __name__ == "__main__":
    asyncio.run(main())