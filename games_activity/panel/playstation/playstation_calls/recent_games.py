from panel.playstation.get_unmonitored_accounts import get_tokens
import asyncio

from panel.playstation.helpers.auth_edit import Authenticator
from panel.playstation.helpers.async_request_builder import AsyncRequestBuilder
from panel.playstation.helpers.proxy_handler_client import ProxySession
import query_builder as qb
import random
from datetime import datetime, timedelta, timezone

from logging_utils import init_logger, logging

logger = init_logger()

ps_db = qb.db(db_name='ps')
ps_public = qb.Schema('public', db_name='ps')
ps_metrics = qb.Schema('metrics', db_name='ps')


NUMBER_OF_TOKENS = 1

async def get_library(builder: AsyncRequestBuilder, account_id: int, sessions=None, retries=3):
    base_uri = f'https://m.np.playstation.com/api/gamelist/v2/users/{account_id}/titles'
    params = {
        'categories': 'ps4_game,ps5_native_game',
        'limit': 200,   # max allowed
        'offset': 0
    }

    all_titles = []

    while True:
        session = random.choice(sessions)
        for attempt in range(retries):
            try:
                response = await builder.get(url=base_uri, session=session, params=params)
                break
            except Exception:
                if attempt == retries - 1:
                    raise
                await asyncio.sleep(1)

        items = response.get("titles", [])
        all_titles.extend(items)

        if len(items) < params["limit"]:
            break

        params["offset"] += params["limit"]

    return all_titles

async def get_proxy_request(number=20):
    import requests
    import random
    url = "https://proxy.ampere-analytics.com/proxy_api/proxies/proxy?proxy_type=datacenter&proxy_type=vpn&country_code=MX&country_code=US"

    proxy_list = []
    for _ in range(number):
        response = requests.get(url)

        
        if response.status_code == 200:
            included_providers = ['rayobyte']
            proxies = response.json()  # or .text if it’s plain text
            proxies = [p for p in proxies if p['provider']['name'] in included_providers]
            while True:
                prox = random.choice(proxies)
                p = prox['proxy']
                cred = prox['provider']['credentials']
                if cred:
                    proxy = {'ip': p['ip'], 'port': p['port'], 'username': cred['username'], 'password': cred['password']}
                    proxy_list.append(proxy)
                    break
        else:
            print(f"Error {response.status_code}: {response.text}")
    return proxy_list

async def get_proxies():
    all_proxies_sessions = []
    proxies = await get_proxy_request()
    for proxy in proxies:
        proxy_data = {
            "proxy": {
                "protocol": "http",
                "ip": proxy['ip'],
                "port": proxy['port'],
                "ssl": False,
                "unique_proxy_id": "custom-proxy-1"
            },
            "provider": {
                "credentials": {
                    "username": proxy['username'],
                    "password": proxy['password']
                },
                "name": "custom"
            }
        }

        proxy_session = await ProxySession.async_init_from_data(
            logger=logging.getLogger(),
            proxy_data=proxy_data,
            do_not_update=True
        )
        all_proxies_sessions.append(proxy_session)
    
    return all_proxies_sessions

async def run():
    try:
        proxy_pool = await get_proxies()

        # Get all available tokens
        tokens = await get_tokens(number_of_tokens=NUMBER_OF_TOKENS)
        print(f"Got {len(tokens)} tokens")

        # Create authenticators and builders for each token
        builders = []
        for i, token in enumerate(tokens):
            try:
                authenticator = await Authenticator.async_init(npsso_token=token, proxy_pool=proxy_pool, logger=logger)
                if authenticator.success:
                    builder = AsyncRequestBuilder(authenticator)
                    builders.append(builder)
                    print(f"Successfully created builder {i+1}")
                else:
                    print(f"Failed to authenticate token {i+1}")
            except Exception as e:
                print(f"Error creating authenticator {i+1}: {e}")

        print(f"Created {len(builders)} working builders")

        if not builders:
            print("No working builders available!")
            return
        
        response = await get_library(builders[0], 104192958940080447, proxy_pool)
        print(response)

    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Close all proxy sessions
        if 'proxy_pool' in locals() and proxy_pool:
            for session in proxy_pool:
                try:
                    await session.close()
                except Exception as e:
                    print(f"Error closing session: {e}")

async def main():
    import time
    time_0 = time.time()
    print("Starting")

    
    await run()
    
    time_1 = time.time()
    print(f"Time: {time_1 - time_0}")

if __name__ == "__main__":
    asyncio.run(main())








