import query_builder as qb
import time
from datetime import datetime, timedelta
from asyncio import Lock

starting_id_lock = Lock()

ps_db = qb.db(db_name='ps')
ps_public = qb.Schema('public', db_name='ps')
ps_metrics = qb.Schema('metrics', db_name='ps')

monitored_file = 'panel/playstation/monitored_account.txt'
last_checked = 'panel/playstation/last_id_checked.txt'
last_checked_a = 'panel/playstation/last_id_checked_a.txt'


async def get_starting_account_id():
    try:
        with open(last_checked, 'r') as f:
            return int(f.read().strip())
    except FileNotFoundError:
        return 0
    
async def get_starting_account_id_a():
    try:
        with open(last_checked_a, 'r') as f:
            return int(f.read().strip())
    except FileNotFoundError:
        return 0

async def save_last_checked(account_id):
    with open(last_checked, 'w') as f:
        f.write(str(account_id))


async def save_last_checked_a(account_id):
    with open(last_checked_a, 'w') as f:
        f.write(str(account_id))

async def get_n_accounts_a(batch_size=10, starting_account_id=0):
    players_table = ps_public.new_player_base
    async with starting_id_lock:
        starting_account_id = await get_starting_account_id_a()
        query = qb.Query(
            qb.select(players_table.c.account_id, players_table.c.country_id)
            .where(
                players_table.c.account_id > starting_account_id
            )
            .order_by(players_table.c.account_id.asc())
            .limit(batch_size)
        )
        results = await query.fetchall()
        account_ids = [r['account_id'] for r in results]

        if account_ids:
            await save_last_checked_a(account_ids[-1])
        return results

async def get_n_accounts(batch_size=10, starting_account_id=0):
    async with starting_id_lock:
        # This entire block is now thread-safe for async workers
        starting_account_id = await get_starting_account_id()
        players_table = ps_public.players
        monitored_table = ps_metrics.daily_monitored_25_08_05

        subq = qb.select(monitored_table.c.account_id)

        query = qb.Query(
            qb.select(players_table.c.account_id, players_table.c.country_id)
            .where(
                players_table.c.account_id > starting_account_id,
                players_table.c.account_id.not_in(subq)
            )
            .order_by(players_table.c.account_id.asc())
            .limit(batch_size)
        )
        results = await query.fetchall()
        account_ids = [r['account_id'] for r in results]

        if account_ids:
            await save_last_checked(account_ids[-1])
        return results

async def get_countries(accountIds: list[str]):
    players_table = ps_public.players
    query = qb.Query(
        qb.select(players_table.c.account_id, players_table.c.country_id)
        .where(
            players_table.c.account_id.in_(accountIds)
        )
    )
    results = await query.fetchall()
    return results


async def get_tokens(number_of_tokens=1):
    import random
    table = ps_public.accounts_sso
    query = qb.Query(
        qb.select(table.c.sso)
        .where(
            table.c.updated_at > datetime.now() - timedelta(days=10)
        )
        .order_by(table.c.updated_at.desc())
        .limit(number_of_tokens*5)
    )
    results = await query.fetchall()
    results = [r['sso'] for r in results]
    tokens = []
    for i in range(number_of_tokens):
        token = random.choice(results)
        results.remove(token)
        tokens.append(token)
    return tokens

async def main():
    print("starting")
    t0 = time.time()
    accounts = await get_n_accounts(batch_size=10000)
    print(accounts[:5])

    # ids = [****************, ****************, ****************]
    # countries = await get_countries(ids)
    # print(countries)
    
    t1 = time.time()
    print(f"Time: {t1-t0}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
