from panel.playstation.get_unmonitored_accounts import get_n_accounts, get_tokens, get_countries, get_n_accounts_a
import asyncio

from panel.playstation.helpers.auth_edit import Authenticator
from panel.playstation.helpers.async_request_builder import AsyncRequestBuilder
from panel.playstation.helpers.proxy_handler_client import ProxySession
import query_builder as qb
import random
from datetime import datetime, timedelta, timezone

from logging_utils import init_logger, logging

ps_db = qb.db(db_name='ps')
ps_public = qb.Schema('public', db_name='ps')
ps_metrics = qb.Schema('metrics', db_name='ps')

BATCH_SIZE = 100
BATCH_COUNT = 10
NUMBER_OF_TOKENS = 10

logger = init_logger()
one_year_ago = datetime.now(timezone.utc) - timedelta(days=365)

platform_map = {
    'ps4': 2,
    'PS5': 1
}

async def check_if_valid(presence):
    if presence and 'accountId' in presence and 'primaryPlatformInfo' in presence:
        if 'platform' in presence['primaryPlatformInfo'] and 'lastOnlineDate' in presence['primaryPlatformInfo']:
            if presence['primaryPlatformInfo']['platform'] in platform_map:
                last_online = datetime.fromisoformat(presence['primaryPlatformInfo']['lastOnlineDate'].replace('Z', '+00:00'))
                if last_online > one_year_ago:
                    return True
    return False

async def get_presence_batch(builder: AsyncRequestBuilder, account_ids, session, worker_id=0):
    """account_ids size must be less than or equal to 100"""
    try:
        params = {
            'type': 'primary',
            'accountIds': ','.join([str(account_id) for account_id in account_ids])
        }
        response = await builder.get(url="https://m.np.playstation.com/api/userProfile/v1/internal/users/basicPresences",
                                     params=params, session=session)
        presences = response['basicPresences']
        # Filter out None/empty presences and extract account IDs
        valid_account_ids = []
        for presence in presences:
            if await check_if_valid(presence):
                valid_account_ids.append({'account_id': int(presence['accountId']), 'platform': platform_map[presence['primaryPlatformInfo']['platform']]})

        # print(f"Worker {worker_id}: Got {len(valid_account_ids)} valid accounts from batch of {len(account_ids)}")
        return valid_account_ids
    except Exception as e:
        # print(f"Worker {worker_id}: Error processing batch: {e}")
        return []

async def get_proxy_request(number=20):
    import requests
    import random
    url = "https://proxy.ampere-analytics.com/proxy_api/proxies/proxy?proxy_type=datacenter&proxy_type=vpn&country_code=MX&country_code=US"

    proxy_list = []
    for _ in range(number):
        response = requests.get(url)

        
        if response.status_code == 200:
            included_providers = ['rayobyte']
            proxies = response.json()  # or .text if it’s plain text
            proxies = [p for p in proxies if p['provider']['name'] in included_providers]
            while True:
                prox = random.choice(proxies)
                p = prox['proxy']
                cred = prox['provider']['credentials']
                if cred:
                    proxy = {'ip': p['ip'], 'port': p['port'], 'username': cred['username'], 'password': cred['password']}
                    proxy_list.append(proxy)
                    break
        else:
            print(f"Error {response.status_code}: {response.text}")
    return proxy_list

async def get_proxies():
    all_proxies_sessions = []
    proxies = await get_proxy_request()
    for proxy in proxies:
        proxy_data = {
            "proxy": {
                "protocol": "http",
                "ip": proxy['ip'],
                "port": proxy['port'],
                "ssl": False,
                "unique_proxy_id": "custom-proxy-1"
            },
            "provider": {
                "credentials": {
                    "username": proxy['username'],
                    "password": proxy['password']
                },
                "name": "custom"
            }
        }

        proxy_session = await ProxySession.async_init_from_data(
            logger=logging.getLogger(),
            proxy_data=proxy_data,
            do_not_update=True
        )
        all_proxies_sessions.append(proxy_session)
    
    return all_proxies_sessions

async def process_worker(worker_id, builder, proxy_pool, batch_size=100, batch_count=2):
    """Worker function to process account batches"""
    collected_accounts = []
    count = 0

    while True:
        try:
            # Get next batch of accounts
            accounts = await get_n_accounts_a(batch_size=batch_size)
            account_ids = [a['account_id'] for a in accounts]
            if not account_ids:
                print(f"Worker {worker_id}: No more accounts to process")
                break

            count += 1
            # print(f"Worker {worker_id}: Processing batch {count} with {len(account_ids)} accounts")

            # Get a random proxy session
            session = random.choice(proxy_pool)

            # Process the batch
            valid_accounts = await get_presence_batch(builder, account_ids, session, worker_id)

            valid_dict = {va['account_id']: va['platform'] for va in valid_accounts}
            results = [
                {
                    'account_id': a['account_id'],
                    'country_id': a['country_id'],
                    'platform_id': valid_dict[a['account_id']]
                }
                for a in accounts
                if a['account_id'] in valid_dict
            ]

            collected_accounts.extend(results)

            # print(f"Worker {worker_id}: Total collected so far: {len(collected_accounts)}")

            # Small delay to avoid overwhelming the API
            await asyncio.sleep(1)
            if count >= batch_count:
                break

        except Exception as e:
            print(f"Worker {worker_id}: Error in batch {count}: {e}")
            await asyncio.sleep(5)  # Wait longer on error
            continue

    print(f"Worker {worker_id}: Finished. Collected {len(collected_accounts)} total accounts")
    return collected_accounts

async def insert_players_stmt(to_add):
    seen = set()
    unique_accounts = []
    for account in to_add:
        if account['account_id'] not in seen:
            seen.add(account['account_id'])
            unique_accounts.append(account)
    
    if not unique_accounts:
        return

    print(f"inserting {len(unique_accounts)} new players")
    insert_stmt = qb.insert(ps_public.new_player_base_a).values(unique_accounts)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_update(
            index_elements=[ps_public.new_player_base_a.c.account_id],
            set_={ 'country_id': insert_stmt.excluded.country_id, 'platform_id': insert_stmt.excluded.platform_id }
            )
    )
    await qb.engine(insert_stmt, db_name='ps').execute()

async def run():
    try:
        proxy_pool = await get_proxies()

        # Get all available tokens
        tokens = await get_tokens(number_of_tokens=NUMBER_OF_TOKENS)
        print(f"Got {len(tokens)} tokens")

        # Create authenticators and builders for each token
        builders = []
        for i, token in enumerate(tokens):
            try:
                authenticator = await Authenticator.async_init(npsso_token=token, proxy_pool=proxy_pool, logger=logger)
                if authenticator.success:
                    builder = AsyncRequestBuilder(authenticator)
                    builders.append(builder)
                    print(f"Successfully created builder {i+1}")
                else:
                    print(f"Failed to authenticate token {i+1}")
            except Exception as e:
                print(f"Error creating authenticator {i+1}: {e}")

        print(f"Created {len(builders)} working builders")

        if not builders:
            print("No working builders available!")
            return

        # Start workers in parallel
        tasks = []
        for i, builder in enumerate(builders):
            task = asyncio.create_task(process_worker(i, builder, proxy_pool, batch_size=BATCH_SIZE, batch_count=BATCH_COUNT))
            tasks.append(task)

        # Wait for all workers to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)


        # Collect all account IDs
        all_collected_accounts = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"Worker {i} failed with exception: {result}")
            else:
                all_collected_accounts.extend(result)
        
        await insert_players_stmt(all_collected_accounts)

    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Close all proxy sessions
        if 'proxy_pool' in locals() and proxy_pool:
            for session in proxy_pool:
                try:
                    await session.close()
                except Exception as e:
                    print(f"Error closing session: {e}")

async def main():
    import time
    time_0 = time.time()
    print("Starting")

    total_runs = 25
    for i in range(total_runs):
        t0 = time.time()
        print(f"Run {i+1}/{total_runs}")
        await run()
        t1 = time.time()
        print(f"Run {i+1} took {t1-t0}s")
    
    time_1 = time.time()
    print(f"Time: {time_1 - time_0}")

if __name__ == "__main__":
    asyncio.run(main())
