import logging
from time import time
from urllib.parse import urlparse, parse_qs

import random
import requests
from panel.playstation.helpers.proxy_handler_client import ProxySession, RequestFailedException, ProxyPool
from typing import List
import pprint


class AuthenticationError(Exception):
    pass


class TokenUpdateError(Exception):
    pass


token_update_retries = 3


# Class Authenticator
# Responsible for authentication using npsso code
# Also used later to get access token from refresh token
# For internal use only do not call directly


class Authenticator:
    URLS = {
        "BASE_URI": "https://ca.account.sony.com/api",
        "CLIENT_ID": "********-7237-4370-9b40-3806e67c0891",
        "SCOPE": "psn:mobile.v2.core psn:clientapp",
        "REDIRECT_URI": "com.scee.psxandroid.scecompcall://redirect",
    }
    npsso_token: str
    proxy_pool: ProxyPool

    @classmethod
    async def async_init(
        cls,
        npsso_token,
        proxy_pool: List[ProxySession] = None,
        retries: int = 3,
        logger: logging.Logger = None,
        account_id: int = None
    ):
        """
        To get 64 character npsso code refer to the README.md

        :param logger:
        :param retries:
        :param proxy_pool:
        :param npsso_token:
        :type npsso_token: str
        """
        self = cls()
        self.account_id = account_id
        self.npsso_token = npsso_token
        self.oauth_token_response = None
        self.access_token_expiration = None
        self.refresh_token_expiration = None
        self.proxy_pool = proxy_pool
        self.logger = logger
        for i in range(retries):
            try:
                await self.authenticate()
                break
            except AuthenticationError as e:
                latest_exception = e
            except Exception as e:
                latest_exception = e
            if i == retries - 1:
                if logger:
                    logger.critical(f"Account ID: {account_id} Could not authenticate: {latest_exception}")
                else:
                    print(f"Could not authenticate: {latest_exception}")
                self.success = False
                return self
        self.success = True
        return self

    async def obtain_fresh_access_token(self):
        """
        Gets a new access token from refresh token

        :returns: str: access token
        """
        if self.is_access_token_expired():
            retries = 0
            latest_exception = None
            while retries < token_update_retries:
                try:
                    data = {
                        "refresh_token": self.oauth_token_response["refresh_token"],
                        "grant_type": "refresh_token",
                        "scope": Authenticator.URLS["SCOPE"],
                        "token_format": "jwt",
                    }
                    auth_header = {
                        "Authorization": "Basic ************************************************************************"
                    }
                    if not self.proxy_pool:
                        response = requests.post(
                            url="{}/authz/v3/oauth/token".format(
                                Authenticator.URLS["BASE_URI"]
                            ),
                            headers=auth_header,
                            data=data,
                        )
                        self.oauth_token_response = response.json()
                    else:
                        while True:
                            session = random.choice(self.proxy_pool)
                            try:
                                response = await session.post(
                                    url="{}/authz/v3/oauth/token".format(
                                        Authenticator.URLS["BASE_URI"]
                                    ),
                                    headers=auth_header,
                                    data=data,
                                )
                            except RequestFailedException:
                                try:
                                    # remove proxy from list
                                    # self.proxy_pool.remove(session)
                                    pass
                                except ValueError:
                                    pass
                                continue
                            break
                        self.oauth_token_response = await response.json()
                    self.access_token_expiration = (
                        time() + self.oauth_token_response["expires_in"]
                    )
                    self.refresh_token_expiration = (
                        time() + self.oauth_token_response["refresh_token_expires_in"]
                    )
                    if self.oauth_token_response["refresh_token_expires_in"] <= 259200:
                        print(
                            "Warning: Your refresh token is going to expire in less than 3 days. Please renew you npsso token!"
                        )
                    return self.oauth_token_response["access_token"]
                except Exception as e:
                    latest_exception = e
                    retries += 1
            raise TokenUpdateError(
                f"Could not update token. Exception: {latest_exception}"
            )
        else:
            return self.oauth_token_response["access_token"]

    # returns the access code
    def get_access_token(self):
        """
        Gets the access token value

        :returns: str: access token value
        """
        return self.oauth_token_response["access_token"]

    # Tells how much times remain till access token expires
    def access_token_expires_in(self):
        """
        Checks how much time is remaining till expiration

        :returns: float: time remaining in seconds
        """
        return time() - self.access_token_expiration

    # Tells if access token has expired
    def is_access_token_expired(self):
        """
        Checks if the access token is expired

        :returns: Boolean
        """
        if self.oauth_token_response is None:
            return True
        return time() + 100 >= self.access_token_expiration

    # Tells how much times remain till access refresh expires
    def refresh_token_expires_in(self):
        """
        Checks how much time is remaining till expiration

        :returns: float: time remaining in seconds
        """
        return time() - self.refresh_token_expiration

    # Tells if refresh token has expired
    def is_refresh_token_expired(self):
        """
        Checks if the access token is expired

        :returns: Boolean
        """
        return time() >= self.refresh_token_expiration

    # Obtain oauth token if the code is passed otherwise refreshes the access token using refresh token
    async def oauth_token(self, code):
        """
        Internal Function, Do not call directly.

        :param code: Code obtained using npsso code
        """

        data = {
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": Authenticator.URLS["REDIRECT_URI"],
            "scope": Authenticator.URLS["SCOPE"],
            "token_format": "jwt",
        }

        auth_header = {
            "Authorization": "Basic ************************************************************************"
        }

        if not self.proxy_pool:
            response = requests.post(
                url="{}/authz/v3/oauth/token".format(Authenticator.URLS["BASE_URI"]),
                headers=auth_header,
                data=data,
            )
            self.oauth_token_response = response.json()
        else:
            while True:
                session = random.choice(self.proxy_pool)
                try:
                    response = await session.post(
                        url="{}/authz/v3/oauth/token".format(
                            Authenticator.URLS["BASE_URI"]
                        ),
                        headers=auth_header,
                        data=data,
                    )
                    if response.status == 406:
                        raise RequestFailedException("406")
                    if response.status == 403:
                        raise RequestFailedException("403 Forbidden")
                except RequestFailedException:
                    try:
                        # self.proxy_pool.remove(session)
                        pass
                    except ValueError:
                        pass
                    continue
                break
            self.oauth_token_response = await response.json()
        self.access_token_expiration = time() + self.oauth_token_response["expires_in"]
        self.refresh_token_expiration = (
            time() + self.oauth_token_response["refresh_token_expires_in"]
        )
        if self.oauth_token_response["refresh_token_expires_in"] <= 259200:
            print(
                "Warning: Your refresh token is going to expire in less than 3 days. Please renew you npsso token!"
            )

    # Authenticate using npsso
    async def authenticate(self):
        """
        Authenticate using the npsso code provided in the constructor

        Obtains the access code and the refresh code. Access code lasts about 1 hour. While the refresh code lasts
        about 2 months. After 2 months a new npsso code is needed.

        :raises PSNAWPAuthenticationException: If authentication is unsuccessful
        """
        print("authenticating")
        cookies = {"Cookie": "npsso=" + self.npsso_token}
        params = {
            "access_type": "offline",
            "client_id": Authenticator.URLS["CLIENT_ID"],
            "scope": Authenticator.URLS["SCOPE"],
            "redirect_uri": Authenticator.URLS["REDIRECT_URI"],
            "response_type": "code",
        }
        if not self.proxy_pool:
            response = requests.get(
                url="{}/authz/v3/oauth/authorize".format(
                    Authenticator.URLS["BASE_URI"]
                ),
                headers=cookies,
                params=params,
                allow_redirects=False,
            )
        else:
            while True:
                session = random.choice(self.proxy_pool)
                print(session)
                try:
                    response = None
                    response = await session.get(
                        url="{}/authz/v3/oauth/authorize".format(
                            Authenticator.URLS["BASE_URI"]
                        ),
                        headers=cookies,
                        allow_redirects=False,
                        params=params,
                    )
                    print(response.status)
                    if response.status == 406:
                        raise RequestFailedException("406")
                    if response.status == 403:
                        raise RequestFailedException("403 Forbidden")
                except RequestFailedException as e:
                    try:
                        self.logger.critical(f"""
                            RequestFailedException {e}
                            response: {pprint.pprint(response)}
                            cookies: {pprint.pprint(cookies)}
                            params: {pprint.pprint(params)}""")
                        # self.proxy_pool.remove(session)
                    except ValueError:
                        pass
                    continue
                break
        self.auth_response = response
        response.raise_for_status()
        location_url = response.headers["location"]
        parsed_url = urlparse(location_url)
        parsed_query = parse_qs(parsed_url.query)
        if "error" in parsed_query.keys():
            if "4165" in parsed_query["error_code"]:
                raise AuthenticationError(
                    "Your npsso code has expired or is incorrect. "
                    "Please generate a new code!"
                )
            elif "51" in parsed_query["error_code"]:
                raise AuthenticationError(
                    f"Email verification required! sso: {self.npsso_token}"
                )
            elif "103" in parsed_query["error_code"]:
                raise AuthenticationError(
                    f"Error: {parsed_query['error']}, interaction required! Error description: {parsed_query['error_description']}, sso: {self.npsso_token}"
                )
            else:
                raise AuthenticationError("Something went wrong while authenticating")
        headers = dict(response.headers)
        await self.oauth_token(parsed_query["code"][0])
