import asyncio
import csv
from typing import List, Set
import random

from aiohttp import ClientResponseError
from xbox.webapi.api.client import XboxLiveClient
from xbox.webapi.authentication.manager import AuthenticationManager
from xbox.webapi.authentication.models import OAuth2TokenResponse
from panel.xbox.utils.geolocator import get_location
from logging_utils import init_logger
from panel.xbox.utils.proxies import get_proxy_sessions
import query_builder as qb
from panel.xbox.utils.seen_lists import all_seen_users
from queue import Queue

xbox_db = qb.db(db_name='xbox')
xbox_metrics = qb.Schema('metrics', db_name='xbox')
xbox_public = qb.Schema('public', db_name='xbox')

logger = init_logger()
offset_file = 'panel/xbox/offset.txt'
account_path = 'panel/xbox/unmonitored-player-base.tsv'

async def get_clients(sessions):
    try:
        accounts = [
        ["0a475988-97c8-4120-bc0e-ec401e004a58", "****************************************", {"token_type": "bearer", "expires_in": 3600, "scope": "XboxLive.signin XboxLive.offline_access", "access_token": "EwAYA+pvBAAUKods63Ys1fGlwiccIFJ+qE1hANsAAfVFa2LL2kBaWrS/c+JjulStoCo9qRNGh0opFZUOE6GEUVXebyn+vAHjoks0b+D0ow4YQ46t4vBxNh0td5EYIfu09Qjp/LLXFLvg1mqwJyTTqIvvVc/GGcryQteSRobRwPYkr0sPOC9/eAbIPtJTa4WmvT3zf5HvfBCY63i6FjHqt2P28IeCO1pUaNENzWUMjdo0a4sUfrLRYADyey/dTPBxNLdX2eAieuUTg07IhNwZku707WbZEdhxrQd5iWBMPev7D/aBLkSubVeQ7fqlE8fn42E5+5qNKMXzJPA4ECHBNsfPW6AMe7DkuqSKRUg09ElbUUHwIUSux+SEC2xqMB4QZgAAEPlZ/f085qGbkFsAWqKJDa7gAWu39Eh9N/MsP4XS8+XwkUtP02wpjjNcM5w9j8XWLZlGa3/kYB4LE+Rs5+Rg+vCWe0C/B5FoUEsgkQJc8wvtxdvEI5I8/MVYgDpbKL3Al/3zNN+DUZBP+tfHpigwkpai4OFK0d62kIg8PI6mH6VZuRAwcZZDeOX+FZCAsiVR0U3PCxnv2Emm4KyWN5Rrv26p8aHH+3iVnNdCopFmhQHTKtqTjR6MEEHtEz5xMwgmexWPuPl9vTL/e/JtMDo1NHZ+RcfcDOIitvQeTieS6qTAeOwBlsMj2NPVst09ceTOHNNi7zt0Ys5llHufA8eTkQafstL3qsZtbs5IgA6hsQciD4ZoGAKwx0lEqGszCzcBkSoU7e/c1Ev2M5HjOFlvo3C3ZKnyBxK9xPcfC6Y907W4o/27/ng6P1koLU4aMGT2PhiTNEIpzhLOvrUKu53LcM9U8UxZ4YTTZB39f7FL6B7JG0G6X/r0Xlnv7+zMEOS0lmaKooFvTO9RSyzQbJ2m6nBAhAx4CrBNGKRY+dKbtohjmRM51hjpJyQUvJFBb/50zYJ/iD+1Zy2XRZubfsOIRY+VswnIhRxweBTtFFKscTfjTXD3jj/ViZ9qse/p8ezbPhQnUBbzrwzU9Hyewk7+xFOKFRsC", "refresh_token": "M.C514_BAY.0.U.-CmdlZNVwkUuQrrahbwQLhhuVhdc3TBxWAoO1LnmX8pCaMLe35tTM6bYileJneSwqpgWSsZ3ZF7wsJeQvh8ZKysFJxMFD3u2MuR4leLgBfuzVQ1Pyyxu!!EGFQduWz25SByyAInoqPyNzooBhzxrRxE!RU3hnwVE!17s2CvnnPizN!K!rjW!rEVkpuMurcFzb5AnuA9MkEXzk2I43TDabUJRTn4wtZaroGgsg5aenoU7eZkL3c8CEzD1hmx32lgeZ4B9ddgw*RZR9u15nvluLxgdqQcAkZwe2y3tTHd4m*p59XjlfD3enmbNgi77zEM172I0e0LJcbqLJ9CkDZlygxrs$", "user_id": "AAAAAAAAAAAAAAAAAAAAAGUrz5r7Ix6LnzgGwYrRUn4", "issued": "2024-10-25T00:33:23.559182+00:00"}],
        ["10d6ef0e-0d44-441c-8c8c-b122a30483a9", "*************************************", {"token_type": "bearer", "expires_in": 3600, "scope": "XboxLive.signin XboxLive.offline_access", "access_token": "EwA4A+pvBAAUKods63Ys1fGlwiccIFJ+qE1hANsAAbLRlrK52YdNVWxCbxcJqaHO8jzmTIQVkBrLrbpAWP7Cm2gucFYErb2W6hWZk4pvSDY5UBj5vJq8IYkU/bcqtxt3wzG/UFmlEiAgIAwbcvVHnsg7P6nDUcb6LQefvhUKusXrVqhapFbfX/TyKRo1nZaayMKZ6mlJswy4mLTSpO+3AwNbXiGjzjubcOHD+wZWXy5LwgPRDnTYMNKMcIRSfwSMQ9gMAsFLZBO+QUoOzZoWF1W8svW0V45jvgAVQ23QOHX2ooWA5IQMWng4QkAEwcQw7KAw1MLrQtiY+Qm/5eISr2VwPWUAMRbd/fdmpVMoObklc+xysZ67p1sltE1f/U0QZgAAECGUZNWCoVk55xh/gFBKNUEAAkz8ABHxeHONia1mhoT6aGPJpG20/V/7gO3vWcm44VeyUcG4BaEwW+qM4KdEMIBK3du5nBSfu/x44kPFl1AyNShGG59zE5RuJbgTtkeRVQjQ+x0QIzmaYfxu6ZWwWXD1cmRaVzZr1tXhhn7vfwKorVc0LBDPzuBAR5exMcR14TOEtmRB5eu0FDwjYhT96fZUWwhX2+PVfft/ABeRTgCp3hxtGp/aXk86yeZFgZDM8JC44Z3JoIDdQiyo8jFKsExpwxrBxD0nsX/4tpuC6jGY6lFb9+pkNOkNWG7LqQWsQj/VnPdXQ00LNYDPi+Bw0Lb/1X/zT+81gotmL3UYBsqttmBYIZ9y9l+lh2iR7EnDNbMC7kpefOVz9+GGN79b6MaywacDWg6edki7t6MHlZ3IjzFzrSHGkHBmDyJq6cWDLi54Sx8ldeBn/UjTUoWrzHf9cVCLdhrMPRZcu8d1GGS9nJI9sbbICpVbP4F6rGZ1qOqiAQJSUN262LzgzIVW+24hqEMHlSSJExvCX6P47Rbi1kGgja83sw3YNeSejO7H0byqeimPAbS7FlmmP3dstwtfGn3AiLbpZTi6tYhhdrc2JxAUeAFjuKOW6bthCKPqNy0hDdm83l25Bdtg1BUA4E/ss4fuF0XgUyK24qTB7/80zoybHdDDP9CLl9zUZFnNLn4yNgI=", "refresh_token": "M.C525_BAY.0.U.-CocDkJrLNmFQrfdByn15K39jxUNFfAqbOBVMUuP!NJ6YJm2CfHc2VHuD6qFJqS8*p2G0pQRRb09uwjPaKOnO2UhRbQ6tYub*K6kRaWZGlWPZEAZZNbjmYFeQh*SFEXTzYTZ51CcYjE2GzxhRb0N!5SpuAVHaVvh*QaWHcifXPrDA4H33xJnb6FJ0Uilwgq3ro1vrKBVNPbZwWNX6sDhXEwJiI!Lj4vfNwtlh8a0mJ0SAzJz*NT*KeFvi5wyjfY*Z6IUYQsgL6wevVI!fheIlnNFX2N4kTZlgYbB5MM8ZXCwqinKXtNs6E45J7hAeC6BvIaCOS4HJMDdr7QHDtfeDlpc$", "user_id": "AAAAAAAAAAAAAAAAAAAAAHHX_uuhT3hI3LCCMNEk56A", "issued": "2025-01-22T00:23:58.965565+00:00"}]
        ]
        clients = []

        count = -1
        for account in accounts:
            count += 1
            auth_mgr = AuthenticationManager(account[0], account[1], "", proxy_sessions=sessions)
            auth_mgr.oauth = OAuth2TokenResponse.parse_obj(account[2])
            try:
                await auth_mgr.refresh_tokens()
                clients.append(XboxLiveClient(auth_mgr))
                print(f"Added client {count}")
            except ClientResponseError:
                print(f"Could not refresh tokens for {count}")
                if hasattr(auth_mgr, 'session') and auth_mgr.session:
                    await auth_mgr.session.close()
        return clients
    
    except Exception as e:
        print(f"Error in get_clients: {e}")
        if sessions:
            for session in sessions:
                await session.close()
        raise

async def process_xuid_batch(client: XboxLiveClient, xuids: List[str], client_id: int, proxy_sessions=None) -> List[dict]:
    retry_count = 0
    
    while True:
        try:
            print(f"Client {client_id}: Processing batch of {len(xuids)} XUIDs (attempt {retry_count + 1})")
            
            proxy_session = None
            if proxy_sessions:
                proxy_session = random.choice(proxy_sessions)
            
            profile = await client.profile.get_profiles(xuids, session=proxy_session)
            break

        except ClientResponseError as e:
            if e.status == 429:
                print(f"Client {client_id}: Rate limited, sleeping for 10 seconds...")
                await asyncio.sleep(5)
                continue
            else:
                raise e
            
        except Exception as e:
            print(f"Client {client_id}: Error processing batch: {e}")
            retry_count += 1
            try:
                await proxy_session.close()
            except Exception as e:
                print(f"Client {client_id}: Error closing proxy session: {e}")
        
    found_accounts = []
    if not profile:
        return []

    for p in profile.profile_users:
        xuid = p.id
        found_accounts.append(xuid)
        print(xuid)

    print(len(found_accounts))

    
    await asyncio.sleep(5)
    return

async def get_n_accounts(batch_size=1):
    import os
    import csv
    if os.path.exists(offset_file):
        with open(offset_file, "r") as f:
            offset = int(f.read().strip())
    else:
        offset = 0

    batch = []
    with open(account_path, "r", newline="", encoding="utf-8") as f:
        reader = csv.DictReader(f, delimiter="\t")
        for _ in range(offset):
            next(reader, None)
        
        for _ in range(batch_size):
            try:
                batch.append(next(reader))
            except StopIteration:
                break

    new_offset = offset + len(batch)
    with open(offset_file, "w") as f:
        f.write(str(new_offset))

    return batch

async def get_one_friends(client: XboxLiveClient, xuid: str, client_id: int, proxy_sessions=None):
    retry_count = 0
    
    while True:
        try:
            print(f"Processing {xuid} (attempt {retry_count + 1})")
            
            proxy_session = None
            if proxy_sessions:
                proxy_session = random.choice(proxy_sessions)
            else:
                print("No proxy sessions")
                return
            
            if retry_count > 10:
                print("Too many retries, skipping")
                return []

            resp = await client.people.get_friends_by_xuid(xuid, session=proxy_session)
            if resp:
                xuids = [person.xuid for person in resp.people]
                print(f"Found {len(xuids)} friends for {xuid}")
                return xuids
            break

        except ClientResponseError as e:
            if e.status == 429:
                print(f"Rate limited, sleeping for 10 seconds...")
                await asyncio.sleep(5)
                continue
            elif e.status == 403:
                print(e)
                return []

            else:
                raise e
            
        except Exception as e:
            print(f"Error processing batch: {e}")
            retry_count += 1
            try:
                await proxy_session.close()
            except Exception as e:
                print(f"Client {client_id}: Error closing proxy session: {e}")
        
    return []

async def process_xuid_batch(client: XboxLiveClient, xuids: List[str], client_id: int, proxy_sessions=None) -> List[dict]:
    retry_count = 0
    
    while True:
        try:
            # if len xuids > 50, split into batches
            if len(xuids) > 50:
                combined = []
                print("Splitting batch")
                batches = [xuids[i:i + 50] for i in range(0, len(xuids), 50)]
                for batch in batches:
                    friends =await process_xuid_batch(client, batch, client_id, proxy_sessions)
                    combined.extend(friends)
                    print(f"Combined: {len(combined)}")
                return combined

            print(f"Processing batch of {len(xuids)} XUIDs (attempt {retry_count + 1})")
            
            proxy_session = None
            if proxy_sessions:
                proxy_session = random.choice(proxy_sessions)
            
            profile = await client.profile.get_profiles(xuids, session=proxy_session)
            break

        except ClientResponseError as e:
            if e.status == 429:
                print(f"Client {client_id}: Rate limited, sleeping for 10 seconds...")
                await asyncio.sleep(5)
                continue
            else:
                raise e
            
        except Exception as e:
            print(f"Client {client_id}: Error processing batch: {e}")
            retry_count += 1
            try:
                await proxy_session.close()
            except Exception as e:
                print(f"Client {client_id}: Error closing proxy session: {e}")
            
        
    found_accounts = []
    if not profile:
        return []

    for p in profile.profile_users:
        for setting in p.settings:
            if setting.id == 'Location':
                location = setting.value
                if location:
                    found_accounts.append({'xuid': p.id, 'location': location})
                break

    formatted_accounts = []
    for account in found_accounts:
        try:
            country_code = await get_location(account['location'])
            if country_code:
                formatted_accounts.append({
                    'xuid': account['xuid'],
                    'country_code': country_code
                })
        except Exception as e:
            print(f"Error getting location for {account['xuid']}: {e}")

    print(f"Client {client_id}: Found {len(formatted_accounts)} accounts with locations")
    print(f"Client {client_id}: Sleeping for 5 seconds...")
    await asyncio.sleep(5)
    return formatted_accounts

async def insert_players_stmt(to_add, visited: set, queue: Queue):
    seen = set()
    unique_accounts = []
    for account in to_add:
        if account['xuid'] not in seen:
            seen.add(account['xuid'])
            unique_accounts.append(account)
    
    if not unique_accounts:
        return
    
    visited.update(seen)
    for account in unique_accounts:
        queue.put_nowait(account['xuid'])

    print(f"inserting {len(unique_accounts)} new players")
    insert_stmt = qb.insert(xbox_public.new_player_base).values(unique_accounts)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_update(
            index_elements=[xbox_public.new_player_base.c.xuid],
            set_={ 'country_code': insert_stmt.excluded.country_code }
            )
    )
    await qb.engine(insert_stmt, db_name='xbox').execute()

async def get_players_from_country(country_code: str):
    query = (
        qb.Query(xbox_public.player_country_map)
        .select(xbox_public.player_country_map.c.xuid)
        .where(xbox_public.player_country_map.c.country_code == country_code)
    )
    results = await query.fetchall()
    return {r['xuid'] for r in results}

async def main():
    seen_users = await all_seen_users()
    visited = set()
    queue = Queue()
    from_china = await get_players_from_country('CN')

    all_proxy_sessions = await get_proxy_sessions()
    clients = await get_clients(sessions=all_proxy_sessions)
            
    if not clients:
        print("No clients available, exiting")
        return
    
    total_epoch = 25
    try:
        for i in range(total_epoch):
            print(f"Epoch {i+1}/{total_epoch}")
            print(f"Queue size: {queue.qsize()}")
            account = ""

            if queue.empty():
                account = random.choice(list(from_china))
                print(f"Using random account: {account}")
            else:
                account = queue.get_nowait()
                print(f"Using queue account: {account}")
            
            friends = await get_one_friends(clients[0], account, 0, all_proxy_sessions)

            # filter already-seen
            friends = [friend for friend in friends if friend not in seen_users]
            friends = [friend for friend in friends if friend not in visited]

            if friends:
                formatted_friends_profile = await process_xuid_batch(clients[0], friends, 0, all_proxy_sessions)
                await insert_players_stmt(formatted_friends_profile, visited, queue)
            else:
                print("No new friends")

            print(f"Total visited: {len(visited)}")

    finally:
        try:
            if all_proxy_sessions:
                for session in all_proxy_sessions:
                    await session.close()
        except Exception as e:
            print(f"Error closing proxy sessions: {e}")


if __name__ == '__main__':
    asyncio.run(main())
