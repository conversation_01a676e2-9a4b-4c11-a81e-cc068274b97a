import query_builder as qb
import asyncio
from tqdm import tqdm
from datetime import datetime, timedelta

xbox_db = qb.db(db_name='xbox')
xbox_metrics = qb.Schema('metrics', db_name='xbox')
xbox_public = qb.Schema('public', db_name='xbox')

async def get_monitored_user() -> set:
    users = set()
    table_name = f'daily_monitored_25_09_20'
    print(f"Getting table {table_name}")
    try:
        table = getattr(xbox_metrics, table_name)
        account_id_result = await qb.Query(table).select('xuid').distinct().fetchall()
        # Convert all user IDs to strings to ensure consistent type
        users.update(str(r['xuid']) for r in account_id_result)
    except AttributeError:
        print(f"Table {table_name} not found")
    return users

async def get_visited_list() -> set:
    users = set()
    table_name = f'new_player_base'
    print(f"Getting table {table_name}")
    try:
        table = getattr(xbox_public, table_name)
        account_id_result = await qb.Query(table).select('xuid').distinct().fetchall()
        # Convert all user IDs to strings to ensure consistent type
        users.update(str(r['xuid']) for r in account_id_result)
    except AttributeError:
        print(f"Table {table_name} not found")
    return users

async def all_seen_users() -> set:
    users = set()
    monitored_users = await get_monitored_user()
    visited_users = await get_visited_list()
    users.update(monitored_users)
    users.update(visited_users)
    return users

async def main():
    await all_seen_users()


if __name__ == "__main__":
    asyncio.run(main())

