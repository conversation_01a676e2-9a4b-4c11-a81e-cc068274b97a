import asyncio
import traceback
import json
import aiohttp

from async_timeout import timeout

# 253327 4790395904
# 253327 5128999936
# 1 in 3
#
# 25354 05094174720
# 25354 73813651455
# 1 in 270

async def get_location(raw_location):
    try:
        async with timeout(10):
            async with aiohttp.ClientSession() as session:
                async with session.get(f'https://nominatim.openstreetmap.org/search?format=json&limit=1&q={raw_location}') as response:
                    data = await response.json()
                    if data:
                        # if there is a data[0] return if not skip
                        if data[0]:
                            return await get_code(data[0]["lat"], data[0]['lon'])
                    else:
                        # print("location not found")
                        pass
    except Exception as e:
        print(f'Error: {e}')
        print(traceback.format_exc())
        return None
    
async def get_code(lat, lon):
    try:
        async with timeout(10):
            async with aiohttp.ClientSession() as session:
                async with session.get(f'https://nominatim.openstreetmap.org/reverse?format=json&limit=1&lat={lat}&lon={lon}') as response:
                    response_text = await response.text()
                    data = json.loads(response_text)
                    if data:
                        # print(data['address']['country_code'])
                        return data['address']['country_code']
    except Exception as e:
        print(f'Error: {e}')
        print(traceback.format_exc())
        return None


async def main():
    raw_location = 'china and sweden'
    await get_location(raw_location)
    
if __name__ == "__main__":
    asyncio.run(main())

