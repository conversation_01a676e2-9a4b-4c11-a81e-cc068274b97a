# checks steam_ids in panel/steam/unmonitored-player-base.tsv to see if they are valid
# if so, save to player.new_player_base

import asyncio
import random
import re
import aiohttp
import query_builder as qb
from library.steam_library.proxies import get_proxies
from datetime import datetime, timedelta
import pytz
import pandas as pd
from asyncio import Lock

from logging_utils import init_logger

steam_player = qb.Schema('player', db_name='steam')
steam_libraries = qb.Schema('library', db_name='steam')
steam_metrics = qb.Schema('metrics', db_name='steam')

account_path = 'panel/steam/unmonitored-player-base.tsv'
offset_file = 'panel/steam/offset.txt'

BATCH_SIZE = 10
BATCH_COUNT = 10
PROXY_TIMEOUT = 23

logger = init_logger()

starting_id_lock = Lock()

HEADERS = {
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-encoding": "gzip, deflate, br, zstd",
    "accept-language": "en-US,en;q=0.9",
    "connection": "keep-alive",
    "host": "steamcommunity.com",
    "sec-ch-ua-platform": "Windows",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "none",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

MINIPROFILE_HEADERS = {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "Accept-Language": "en-US,en;q=0.9",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Host": "steamcommunity.com",
    "Pragma": "no-cache",
    "Referer": "https://steamcommunity.com/search/groups/",
    "sec-ch-ua": '"Chromium";v="106", "Google Chrome";v="106", "Not;A=Brand";v="99"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "X-Requested-With": "XMLHttpRequest"
}

async def get_cookies(min_id=41):
    now = datetime.now(tz=pytz.UTC)
    account_cookies = await qb.Query(steam_libraries.account_cookies).fetchall()
    cookies = []
    for c in account_cookies:
        if c['last_updated'] > now - timedelta(hours=PROXY_TIMEOUT):
            if c['account_pk'] < min_id:
                continue
            cookies.append(await format_cookies(c['cookie']))
    return cookies

async def format_cookies(cookie:list):
    pairs = cookie.split(";")
    cookie_dict = {}
    for pair in pairs:
        key, value = pair.split("=")
        cookie_dict[key.strip()] = value.strip('"')
    return (cookie_dict)

async def check_id(steam_id, cookie, proxy_url):
    url = f"https://steamcommunity.com/profiles/{steam_id}/?xml=1"

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=HEADERS, cookies=cookie, proxy=proxy_url) as response:
                if response.status != 200:
                    print(f"Failed to fetch profile. Status code: {response.status}")
                    return False

                xml_content = await response.text()
       
                location_match = re.search(r'<location>\s*<!\[CDATA\[(.*?)\]\]>\s*</location>', xml_content)
                if location_match:
                    country_name = location_match.group(1).strip()
                    if country_name:
                        return True
                return False
            
    except Exception as e:
        print(e)
        return False


async def check_steam_account_batch(steam_ids, cookie, proxy_url, worker_id=0):
    valid_accounts = []
    for steam_id in steam_ids:
        asyncio.sleep(1)
        try:
            if await check_id(steam_id, cookie, proxy_url):
                valid_accounts.append(steam_id)
        except Exception as e:
            logger.error(f"Worker {worker_id}: Error checking steam id {steam_id}: {e}")
    return valid_accounts

async def get_n_accounts(batch_size=1):
    import os
    import csv
    if os.path.exists(offset_file):
        with open(offset_file, "r") as f:
            offset = int(f.read().strip())
    else:
        offset = 0

    batch = []
    with open(account_path, "r", newline="", encoding="utf-8") as f:
        reader = csv.DictReader(f, delimiter="\t")
        for _ in range(offset):
            next(reader, None)
        
        for _ in range(batch_size):
            try:
                batch.append(next(reader))
            except StopIteration:
                break

    new_offset = offset + len(batch)
    with open(offset_file, "w") as f:
        f.write(str(new_offset))

    return batch

async def process_worker(worker_id, cookie, proxies, batch_size=BATCH_SIZE, batch_count=BATCH_COUNT):
    """Worker function to process account batches"""
    collected_accounts = []
    count = 0

    while True:
        try:
            accounts = await get_n_accounts(batch_size=batch_size)
            steam_ids = [a['steam_id'] for a in accounts]
            if not steam_ids:
                print(f"Worker {worker_id}: No more accounts to process")
                break
        
            count += 1
            print(f"Worker {worker_id}: Processing batch {count} with {len(steam_ids)} accounts")

            # Get a random proxy session
            proxy = random.choice(proxies)
            proxy_url = f"http://{proxy['username']}:{proxy['password']}@{proxy['address']}:{proxy['port']}/"

            # Process the batch
            valid_accounts = await check_steam_account_batch(steam_ids, cookie, proxy_url, worker_id)
            results = [entry for entry in accounts if entry['steam_id'] in valid_accounts]

            collected_accounts.extend(results)

            print(f"Worker {worker_id}: Total collected so far: {len(collected_accounts)}")

            # Small delay to avoid overwhelming the API
            await asyncio.sleep(1)
            if count >= batch_count:
                break

        except Exception as e:
            print(f"Worker {worker_id}: Error in batch {count}: {e}")
            await asyncio.sleep(5)  # Wait longer on error
            continue

    return collected_accounts

async def insert_players_stmt(to_add):
    seen = set()
    unique_accounts = []
    for account in to_add:
        if account['steam_id'] not in seen:
            seen.add(account['steam_id'])
            unique_accounts.append(account)

    if not unique_accounts:
        return
    
    print(f"inserting {len(unique_accounts)} new players")
    insert_stmt = qb.insert(steam_player.new_player_base).values(unique_accounts)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_update(
            index_elements=[steam_player.new_player_base.c.steam_id],
            set_={ 'miniprofile_id': insert_stmt.excluded.miniprofile_id, 'country_pk': insert_stmt.excluded.country_pk }
            )
    )
    await qb.engine(insert_stmt, db_name='steam').execute()

async def run():
    try:
        proxies = await get_proxies()

        cookies = await get_cookies()
        print(f"Running with {len(cookies)} cookies")

        tasks = []
        for i, cookie in enumerate(cookies):
            task = asyncio.create_task(process_worker(i, cookie, proxies))
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)
        all_collected_accounts = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Worker {i} failed with exception: {result}")
            else:
                all_collected_accounts.extend(result)

        await insert_players_stmt(all_collected_accounts)

    except Exception as e:
        logger.error(f"Error in main: {e}")
    

async def main():
    """Entry point with timing and multiple runs"""
    import time

    logger.info("Starting Steam account checker")
    time_0 = time.time()

    total_runs = 1
    for i in range(total_runs):
        t0 = time.time()
        logger.info(f"Run {i+1}/{total_runs}")
        await run()
        t1 = time.time()
        logger.info(f"Run {i+1} took {t1-t0:.2f}s")

    time_1 = time.time()
    logger.info(f"Total time: {time_1 - time_0:.2f}s")

if __name__ == "__main__":
    asyncio.run(main())

