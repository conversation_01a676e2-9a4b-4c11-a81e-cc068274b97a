#!/usr/bin/env python3
"""
Test script for check_steam_ids.py functionality
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from panel.steam.check_steam_ids import (
    get_cookies, 
    get_proxy_pool, 
    check_steam_account_batch,
    extract_country_code,
    get_miniprofile_id
)

async def test_basic_functionality():
    """Test basic functionality with a few known Steam IDs"""
    print("Testing basic Steam account checking functionality...")
    
    # Test with some known Steam IDs (these are public Steam IDs)
    test_steam_ids = [
        *****************,  # First Steam ID + 1
        *****************,  # First Steam ID + 2
        *****************,  # First Steam ID + 3
    ]
    
    try:
        # Get proxies
        print("Getting proxies...")
        proxies = await get_proxy_pool()
        if not proxies:
            print("❌ No proxies available")
            return False
        print(f"✅ Got {len(proxies)} proxies")
        
        # Get cookies
        print("Getting cookies...")
        cookies = await get_cookies()
        if not cookies:
            print("❌ No cookies available")
            return False
        print(f"✅ Got {len(cookies)} cookies")
        
        # Test individual account checking
        print("Testing individual account checking...")
        cookie = cookies[0]
        proxy = proxies[0]
        proxy_url = f"http://{proxy['username']}:{proxy['password']}@{proxy['address']}:{proxy['port']}/"
        
        for steam_id in test_steam_ids[:1]:  # Test just one to avoid rate limiting
            print(f"Checking Steam ID: {steam_id}")
            
            # Test country extraction
            country = await extract_country_code(steam_id, cookie, proxy_url)
            print(f"  Country result: {country}")
            
            # Test miniprofile extraction
            miniprofile = await get_miniprofile_id(steam_id, cookie, proxy_url)
            print(f"  Miniprofile result: {miniprofile}")
            
            await asyncio.sleep(1)  # Be nice to Steam
        
        # Test batch processing
        print("Testing batch processing...")
        results = await check_steam_account_batch(test_steam_ids[:2], cookie, proxies, worker_id=999)
        print(f"✅ Batch processing returned {len(results)} results")
        for result in results:
            print(f"  Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🧪 Starting Steam account checker tests...")
    print("=" * 50)
    
    success = await test_basic_functionality()
    
    print("=" * 50)
    if success:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    return success

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
