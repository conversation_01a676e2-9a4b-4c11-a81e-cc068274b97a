import asyncio
import random
import re
import aiohttp
from lxml import html
import query_builder as qb
from library.steam_library.proxies import get_proxy
from datetime import datetime, timedelta
import pytz
import pandas as pd
import itertools
from asyncio import Queue, QueueEmpty

steam_player = qb.Schema('player', db_name='steam')
steam_libraries = qb.Schema('library', db_name='steam')
PROXY_TIMEOUT = 23
HEADERS = {
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-encoding": "gzip, deflate, br, zstd",
    "accept-language": "en-US,en;q=0.9",
    "connection": "keep-alive",
    "host": "steamcommunity.com",
    "sec-ch-ua-platform": "Windows",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "none",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

MINIPROFILE_HEADERS = {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "Accept-Language": "en-US,en;q=0.9",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Host": "steamcommunity.com",
    "Pragma": "no-cache",
    "Referer": "https://steamcommunity.com/search/groups/",
    "sec-ch-ua": '"Chromium";v="106", "Google Chrome";v="106", "Not;A=Brand";v="99"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "X-Requested-With": "XMLHttpRequest"
}

async def get_cookies(min_id=41):
    now = datetime.now(tz=pytz.UTC)
    account_cookies = await qb.Query(steam_libraries.account_cookies).fetchall()
    cookies = []
    for c in account_cookies:
        if c['last_updated'] > now - timedelta(hours=PROXY_TIMEOUT):
            if c['account_pk'] < min_id:
                continue
            cookies.append(await format_cookies(c['cookie']))
    return cookies

async def format_cookies(cookie:list):
    pairs = cookie.split(";")
    cookie_dict = {}
    for pair in pairs:
        key, value = pair.split("=")
        cookie_dict[key.strip()] = value.strip('"')
    return (cookie_dict)

async def extract_country_code(steam_id, cookie, proxy_url):

    
    url = f"https://steamcommunity.com/profiles/{steam_id}/?xml=1"

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=HEADERS, cookies=cookie, proxy=proxy_url) as response:
                if response.status != 200:
                    print(f"Failed to fetch profile. Status code: {response.status}")
                    return None

                xml_content = await response.text()
       
                location_match = re.search(r'<location>\s*<!\[CDATA\[(.*?)\]\]>\s*</location>', xml_content)
                if location_match:
                    country_name = location_match.group(1).strip()
                    if country_name:
                        return country_name
                return None
            
    except Exception as e:
        return None


async def get_miniprofile_id(steam_id, cookie, proxy_url):
    url = f"https://steamcommunity.com/profiles/{steam_id}"

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=MINIPROFILE_HEADERS, cookies=cookie, proxy=proxy_url) as response:
                resp_text = await response.text()
    except Exception as e:
        return None

    miniprofile_search = re.search(r"<div\s+class=\"playerAvatar\s+profile_header_size\s+[\w-]+\"\s+data-miniprofile=\"(\d+)\">", resp_text)
    if miniprofile_search:
        miniprofile_id = int(miniprofile_search.group(1))
        return miniprofile_id
    else:
        return None

async def get_country_name_to_letter_mappings():
    country_mappings_path = 'panel/steam/cleaned_country_mapping.csv'
    country_mappings = pd.read_csv(country_mappings_path)
    country_mappings = country_mappings.set_index('country_name')['country_letter_code'].to_dict()
    return country_mappings

async def get_country_names():
    country_mappings_path = 'panel/steam/cleaned_country_mapping.csv'
    country_mappings = pd.read_csv(country_mappings_path)
    country_names = country_mappings['country_name'].tolist()
    return country_names

async def get_country_code_mappings():
    country_mappings_path = 'panel/steam/country_code_mappings.csv'
    country_mappings = pd.read_csv(country_mappings_path)
    country_mappings = country_mappings.set_index('country_name')['pk'].to_dict()
    return country_mappings

async def check_id(steam_id, cookie):
    valid_proxy = False
    count = 0
    while not valid_proxy:
        proxy = await get_proxy()
        if proxy:
            valid_proxy = True
        else:
            count += 1
        if count > 4:
            return None

    proxy_url = f"http://{proxy['username']}:{proxy['password']}@{proxy['address']}:{proxy['port']}/"
    country_code_mappings = await get_country_code_mappings()
    country_name_to_letter_mappings = await get_country_name_to_letter_mappings()
    country_names = await get_country_names()  

    country = await extract_country_code(steam_id, cookie, proxy_url)
    if country:
        for c_name in country_names:
            if c_name in country:
                country_letter = country_name_to_letter_mappings[c_name]
                country_code = country_code_mappings[country_letter]
                miniprofile_id = await get_miniprofile_id(steam_id, cookie, proxy_url)
                if miniprofile_id:
                    return {'steam_id': steam_id, 'miniprofile_id': miniprofile_id, 'country_pk': country_code}
                else:
                    return None
        else:
            return None
    else:
        return None

async def insert_new_players(result_queue:Queue):
    if result_queue.qsize() < 100:
        return
    to_add = []
    while not result_queue.empty():
        to_add.append(result_queue.get_nowait())
    await insert_players_stmt(to_add)

    
async def insert_players_stmt(to_add):
    print(f"inserting {len(to_add)} new players")
    insert_stmt = qb.insert(steam_player.new_player_base).values(to_add)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_update(
            index_elements=[steam_player.new_player_base.c.steam_id],
            set_={ 'miniprofile_id': insert_stmt.excluded.miniprofile_id, 'country_pk': insert_stmt.excluded.country_pk }
            )
    )
    await qb.engine(insert_stmt, db_name='steam').execute()

async def process(account_queue:Queue, result_queue:Queue, cookie):
    while True:
        try:
            steam_id = account_queue.get_nowait()
        except QueueEmpty:
            break
        result = await check_id(steam_id, cookie)
        if result:
            await result_queue.put(result)
            await insert_new_players(result_queue)

async def main():
    last_steam_id_path = "panel/steam/last_steam_id.txt"
    try:
        with open(last_steam_id_path, "r") as f:
            STEAM_ID = int(f.read().strip())
    except FileNotFoundError:
        return
    
    cookies = await get_cookies()
    if not cookies:
        print("No valid cookies found. Cannot proceed without authentication.")
        return None
    print(f"running with {len(cookies)} cookies")
    
    account_queue = Queue()
    for steam_id in itertools.islice(itertools.count(STEAM_ID), 50000):
        account_queue.put_nowait(steam_id)
    result_queue = Queue()

    import time
    t0 = time.time()

    print(f"starting at {STEAM_ID}")
    await asyncio.gather(*[process(account_queue, result_queue, cookie) for cookie in cookies])
    to_add = []
    while not result_queue.empty():
        to_add.append(result_queue.get_nowait())
    if len(to_add) > 0:
        await insert_players_stmt(to_add)

    print(f'took {time.time()-t0}s')

    
    print(f"ending at {steam_id}")
    with open(last_steam_id_path, "w") as f:
        f.write(str(steam_id))
            

if __name__ == "__main__":
    asyncio.run(main())

