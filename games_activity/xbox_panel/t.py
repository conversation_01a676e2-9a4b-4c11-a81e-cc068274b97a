import asyncio
import random
import time

from aiohttp import ClientSession, ClientResponseError
from xbox.webapi.api.client import XboxLiveClient
from xbox.webapi.authentication.manager import AuthenticationManager
from xbox.webapi.authentication.models import OAuth2TokenResponse
from xbox.webapi.api.provider.achievements.models import Achievement360, Achievement
from asyncio import Queue, QueueEmpty
from typing import List
from logging_utils import init_logger

import uvloop

from xbox_panel.proxy_handler_client import init_proxy_pool, RequestFailedException
from xbox.webapi.common.signed_session import SignedSession

import time
import json
import csv
import os

logger = init_logger()

async def get_clients():
    filter_providers = ['hola_premium']
    sessions = await init_proxy_pool(-1, filter_providers, logger=logger)

    accounts = [
        ["0a475988-97c8-4120-bc0e-ec401e004a58", "****************************************", {"token_type": "bearer", "expires_in": 3600, "scope": "XboxLive.signin XboxLive.offline_access", "access_token": "EwAYA+pvBAAUKods63Ys1fGlwiccIFJ+qE1hANsAAfVFa2LL2kBaWrS/c+JjulStoCo9qRNGh0opFZUOE6GEUVXebyn+vAHjoks0b+D0ow4YQ46t4vBxNh0td5EYIfu09Qjp/LLXFLvg1mqwJyTTqIvvVc/GGcryQteSRobRwPYkr0sPOC9/eAbIPtJTa4WmvT3zf5HvfBCY63i6FjHqt2P28IeCO1pUaNENzWUMjdo0a4sUfrLRYADyey/dTPBxNLdX2eAieuUTg07IhNwZku707WbZEdhxrQd5iWBMPev7D/aBLkSubVeQ7fqlE8fn42E5+5qNKMXzJPA4ECHBNsfPW6AMe7DkuqSKRUg09ElbUUHwIUSux+SEC2xqMB4QZgAAEPlZ/f085qGbkFsAWqKJDa7gAWu39Eh9N/MsP4XS8+XwkUtP02wpjjNcM5w9j8XWLZlGa3/kYB4LE+Rs5+Rg+vCWe0C/B5FoUEsgkQJc8wvtxdvEI5I8/MVYgDpbKL3Al/3zNN+DUZBP+tfHpigwkpai4OFK0d62kIg8PI6mH6VZuRAwcZZDeOX+FZCAsiVR0U3PCxnv2Emm4KyWN5Rrv26p8aHH+3iVnNdCopFmhQHTKtqTjR6MEEHtEz5xMwgmexWPuPl9vTL/e/JtMDo1NHZ+RcfcDOIitvQeTieS6qTAeOwBlsMj2NPVst09ceTOHNNi7zt0Ys5llHufA8eTkQafstL3qsZtbs5IgA6hsQciD4ZoGAKwx0lEqGszCzcBkSoU7e/c1Ev2M5HjOFlvo3C3ZKnyBxK9xPcfC6Y907W4o/27/ng6P1koLU4aMGT2PhiTNEIpzhLOvrUKu53LcM9U8UxZ4YTTZB39f7FL6B7JG0G6X/r0Xlnv7+zMEOS0lmaKooFvTO9RSyzQbJ2m6nBAhAx4CrBNGKRY+dKbtohjmRM51hjpJyQUvJFBb/50zYJ/iD+1Zy2XRZubfsOIRY+VswnIhRxweBTtFFKscTfjTXD3jj/ViZ9qse/p8ezbPhQnUBbzrwzU9Hyewk7+xFOKFRsC", "refresh_token": "M.C514_BAY.0.U.-CmdlZNVwkUuQrrahbwQLhhuVhdc3TBxWAoO1LnmX8pCaMLe35tTM6bYileJneSwqpgWSsZ3ZF7wsJeQvh8ZKysFJxMFD3u2MuR4leLgBfuzVQ1Pyyxu!!EGFQduWz25SByyAInoqPyNzooBhzxrRxE!RU3hnwVE!17s2CvnnPizN!K!rjW!rEVkpuMurcFzb5AnuA9MkEXzk2I43TDabUJRTn4wtZaroGgsg5aenoU7eZkL3c8CEzD1hmx32lgeZ4B9ddgw*RZR9u15nvluLxgdqQcAkZwe2y3tTHd4m*p59XjlfD3enmbNgi77zEM172I0e0LJcbqLJ9CkDZlygxrs$", "user_id": "AAAAAAAAAAAAAAAAAAAAAGUrz5r7Ix6LnzgGwYrRUn4", "issued": "2024-10-25T00:33:23.559182+00:00"}],
        ["10d6ef0e-0d44-441c-8c8c-b122a30483a9", "*************************************", {"token_type": "bearer", "expires_in": 3600, "scope": "XboxLive.signin XboxLive.offline_access", "access_token": "EwA4A+pvBAAUKods63Ys1fGlwiccIFJ+qE1hANsAAbLRlrK52YdNVWxCbxcJqaHO8jzmTIQVkBrLrbpAWP7Cm2gucFYErb2W6hWZk4pvSDY5UBj5vJq8IYkU/bcqtxt3wzG/UFmlEiAgIAwbcvVHnsg7P6nDUcb6LQefvhUKusXrVqhapFbfX/TyKRo1nZaayMKZ6mlJswy4mLTSpO+3AwNbXiGjzjubcOHD+wZWXy5LwgPRDnTYMNKMcIRSfwSMQ9gMAsFLZBO+QUoOzZoWF1W8svW0V45jvgAVQ23QOHX2ooWA5IQMWng4QkAEwcQw7KAw1MLrQtiY+Qm/5eISr2VwPWUAMRbd/fdmpVMoObklc+xysZ67p1sltE1f/U0QZgAAECGUZNWCoVk55xh/gFBKNUEAAkz8ABHxeHONia1mhoT6aGPJpG20/V/7gO3vWcm44VeyUcG4BaEwW+qM4KdEMIBK3du5nBSfu/x44kPFl1AyNShGG59zE5RuJbgTtkeRVQjQ+x0QIzmaYfxu6ZWwWXD1cmRaVzZr1tXhhn7vfwKorVc0LBDPzuBAR5exMcR14TOEtmRB5eu0FDwjYhT96fZUWwhX2+PVfft/ABeRTgCp3hxtGp/aXk86yeZFgZDM8JC44Z3JoIDdQiyo8jFKsExpwxrBxD0nsX/4tpuC6jGY6lFb9+pkNOkNWG7LqQWsQj/VnPdXQ00LNYDPi+Bw0Lb/1X/zT+81gotmL3UYBsqttmBYIZ9y9l+lh2iR7EnDNbMC7kpefOVz9+GGN79b6MaywacDWg6edki7t6MHlZ3IjzFzrSHGkHBmDyJq6cWDLi54Sx8ldeBn/UjTUoWrzHf9cVCLdhrMPRZcu8d1GGS9nJI9sbbICpVbP4F6rGZ1qOqiAQJSUN262LzgzIVW+24hqEMHlSSJExvCX6P47Rbi1kGgja83sw3YNeSejO7H0byqeimPAbS7FlmmP3dstwtfGn3AiLbpZTi6tYhhdrc2JxAUeAFjuKOW6bthCKPqNy0hDdm83l25Bdtg1BUA4E/ss4fuF0XgUyK24qTB7/80zoybHdDDP9CLl9zUZFnNLn4yNgI=", "refresh_token": "M.C525_BAY.0.U.-CocDkJrLNmFQrfdByn15K39jxUNFfAqbOBVMUuP!NJ6YJm2CfHc2VHuD6qFJqS8*p2G0pQRRb09uwjPaKOnO2UhRbQ6tYub*K6kRaWZGlWPZEAZZNbjmYFeQh*SFEXTzYTZ51CcYjE2GzxhRb0N!5SpuAVHaVvh*QaWHcifXPrDA4H33xJnb6FJ0Uilwgq3ro1vrKBVNPbZwWNX6sDhXEwJiI!Lj4vfNwtlh8a0mJ0SAzJz*NT*KeFvi5wyjfY*Z6IUYQsgL6wevVI!fheIlnNFX2N4kTZlgYbB5MM8ZXCwqinKXtNs6E45J7hAeC6BvIaCOS4HJMDdr7QHDtfeDlpc$", "user_id": "AAAAAAAAAAAAAAAAAAAAAHHX_uuhT3hI3LCCMNEk56A", "issued": "2025-01-22T00:23:58.965565+00:00"}]
        ]
    clients = []

    count = -1
    for account in accounts:
        auth_mgr = AuthenticationManager(account[0], account[1], "", proxy_sessions=sessions)
        auth_mgr.oauth = OAuth2TokenResponse.model_validate(account[2])
        try:
            await auth_mgr.refresh_tokens()
            clients.append(XboxLiveClient(auth_mgr))
            print(f"Added client {count}")
        except ClientResponseError:
            print(f"Could not refresh tokens for {count}")

    return clients

async def get_xuids():
    with open('xbox_panel/xuids.csv', 'r') as f:
        reader = csv.reader(f)
        xuids = list(reader)
    xuids = [xuid[0] for xuid in xuids]
    random.shuffle(xuids)
    return xuids[:68]

async def main(start_index=None, number_to_process=None):

    # filter_providers = ['hola', 'teraVPN', 'soax', 'hola_premium']
    # sessions = await init_proxy_pool(5, filter_providers=filter_providers)

    clients = await get_clients() 
    client = clients[0]

    # xuids = await get_xuids()
    # print("got xuids")

    # profile = await client.profile.get_profiles(
    #         xuids
    #     )
    # for p in profile.profile_users:
    #     for setting in p.settings:
    #         if setting.id == 'Location':
    #             location = setting.value
    #             if location:
    #                 print(f"{p.id} location: {location}")
    #             break
    

if __name__ == '__main__':
    asyncio.run(main(None, 25000))
