{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        // {
        //     "name": "Xbox Presence",
        //     "type": "python",
        //     "request": "launch",
        //     "module": "Xbox.new_presence_monitoring",
        //     "justMyCode": false
        // },
        {
            "name": "PlayStation Presence",
            "type": "python",
            "request": "launch",
            "module": "PlayStation.new_presence_monitoring",
            "justMyCode": false
        },
        {
            "name": "Steam games",
            "type": "python",
            "request": "launch",
            "module": "Steam.games.${fileBasenameNoExtension}",
            "cwd":"${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "Steam groups",
            "type": "python",
            "request": "launch",
            "module": "Steam.groups.${fileBasenameNoExtension}",
            "cwd":"${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "Steam people",
            "type": "python",
            "request": "launch",
            "module": "Steam.people.${fileBasenameNoExtension}",
            "cwd":"${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "Steam presence",
            "type": "python",
            "request": "launch",
            "module": "Steam.presence_monitoring",
            "cwd":"${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "Xbox new presence",
            "type": "python",
            "request": "launch",
            "module": "Xbox.new_presence_monitoring",
            "cwd":"${workspaceFolder}",
            "justMyCode": false
        }
    ]
}