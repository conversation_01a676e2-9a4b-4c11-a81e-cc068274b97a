# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

config.py

*.db
*.sql
*.db-journal

Xbox/trueachievements_gamertags.json

PlayStation/tmp/
PlayStation/plugin/
PlayStation/tmp-upd/

Xbox/tmp/
Xbox/plugin/

panel/steam/unmonitored-player-base.tsv
xbox_panel/xuids.csv
panel/accounts/
panel/xbox/unmonitored-player-base.tsv

tmp/
tmp2/
venv/
config/
*_log.txt

secret/
scrapbook/
wg/
test_venv/
*.env