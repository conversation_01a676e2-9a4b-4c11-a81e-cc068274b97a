version: '3.7'

services:   
  steam-presence:
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    command: python -m Steam.presence_monitoring
    labels:
      - "autoheal=true"
    healthcheck:
      test: python presence_healthcheck.py -p steam -i 30
      interval: 30m
      timeout: 60s
      start_period: 60s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-file: "3"
        max-size: "100m"
    network_mode: "container:games-activity-wireguard-1"

  steam-account-cookie-refresher:
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    # run it once per 11.5 hrs, 60*60*11.5
    command: python -m Steam.account_cookie_refresher 41400
    labels:
      - "autoheal=true"
    healthcheck:
      test: pgrep -f "python -m Steam.account_cookie_refresher" || exit 1
      interval: 5m
      timeout: 30s
      start_period: 30s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-file: "3"
        max-size: "100m"
    extra_hosts:
      - "host.docker.internal:host-gateway"

  steam-statistics-monitor:
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    command: python -m presence_statistics_monitor -p steam -t 15
    logging:
      driver: "json-file"
      options:
        max-file: "3"
        max-size: "100m"
    extra_hosts:
      - "host.docker.internal:host-gateway"

  steam-historical-data-collection:
    build:
      context: .
      dockerfile: Dockerfile
    restart: no
    command: python -m Steam.historical_data_collection
    logging:
      driver: "json-file"
      options:
        max-file: "3"
        max-size: "100m"
    network_mode: "container:games-activity-wireguard-1"

  steam-autoheal:
    image: willfarrell/autoheal:1.1.0
    restart: unless-stopped
    environment:
      AUTOHEAL_INTERVAL: "10"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock"
    network_mode: none

  steam-account-creator:
    build:
      context: .
      dockerfile: Steam/Dockerfile-account-creation
    restart: no
    logging:
      driver: "json-file"
      options:
        max-file: "3"
        max-size: "100m"
    extra_hosts:
      - "host.docker.internal:host-gateway"


networks:
  default:
    name: games-activity_default
    external: true
