import query_builder as qb

class AchievementSchema(qb.Schema):
    _db_name = "steam_test"
    _schema = "achievements"

    class achievements(qb.Table):
        _columns = [
            qb.ID(),
            qb.TableColumn('achievement_name', qb.TEXT, unique=True),
        ]
        _post_create = qb.set_pk_sequence('achievements.achievements', 'achievement_pk')

    class player_achievements(qb.Table):
        _columns = [
            qb.ID(),
            qb.TableColumn('steam_id', qb.BIGINT, qb.ForeignKey()),
            qb.TableColumn('achievement_id', qb.BIGINT, qb.ForeignKey('achievements.achievements.id')),
        ]
        _constraints = [
            qb.UniqueConstraint('achievements.player_achievements.steam_id', 'achievements.player_achievements.achievement_id')
        ]
        # # copy in on create
        #_copy_from = qb.CopyFromOpts('./')

achievements = AchievementSchema()

# # copy in example
# achievements.player_achievements.copy_from_csv(opts=qb.CopyFromOpts('./file.tsv'))

# # insert example
# insert_cmdb = achievements.player_achievements.insert().values()
# qb.engine_sync(insert_cmdb).execute()