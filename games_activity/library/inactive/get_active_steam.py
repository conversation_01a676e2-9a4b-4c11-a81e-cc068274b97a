import query_builder as qb

steam_metrics = qb.Schema('metrics', db_name='steam')
steam_db = qb.db(db_name='steam')

def get_table_names(prefix) -> list[str]:
    tables_result = (
        qb.Query(steam_db.pg_catalog.pg_tables)
            .where(schemaname='metrics')
            .where(qb.c_.tablename.op('~')(prefix+'_(24|25)'))
            .fetchall_sync()
    )
    return [r['tablename'] for r in tables_result]

def get_all_users(table_prefix):
    users = set()
    for table_name in get_table_names(table_prefix):
        print(f"getting table {table_name}")
        try:
            table = getattr(steam_metrics, table_name)
            account_id_result = qb.Query(table).select('player_pk').distinct().fetchall_sync()
            users.update(r['player_pk'] for r in account_id_result)
        except AttributeError:
            continue
    return users

def find_inactive_users():
    active_users = get_all_users('daily_active')
    save_active_users(active_users)
    monitored_users = get_all_users('daily_monitored')
    inactive_users = monitored_users - active_users
    save_inactive_users(inactive_users)
    

def save_inactive_users(inactive_users):
    with open('scrapbook/inactive/steam-inactive-users.tsv', 'w') as f:
        f.write("user_id\n")
        for user in inactive_users:
            f.write(f"{user}\n")
    print(f"Saved {len(inactive_users)} inactive users to scrapbook/inactive/steam-inactive-users.tsv")

def save_active_users(active_users):
    with open('scrapbook/inactive/steam-active-users.tsv', 'w') as f:
        f.write("user_id\n")
        for user in active_users:
            f.write(f"{user}\n")
    print(f"Saved {len(active_users)} active users to scrapbook/inactive/steam-active-users.tsv")

if __name__ == "__main__":
    find_inactive_users()
