#!/usr/bin/env python3
"""
Test script for cookie_refresh_camoufox.py

This script demonstrates how to use the Camoufox-only cookie refresh functionality.
"""

import asyncio
import sys
import os

# Add the parent directory to the path so we can import the module
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from games_activity.library.steam_library.cookie_refresh_camoufox import (
    cookie_refresh_camoufox,
    refresh_cookies_camoufox,
    update_cookies_camoufox
)


async def test_single_account():
    """Test refreshing a single account."""
    print("Testing single account refresh...")
    
    # Replace with real credentials for testing
    email = "<EMAIL>"
    username = "testuser"
    password = "testpassword"
    
    try:
        cookie = await cookie_refresh_camoufox(email, username, password)
        
        if cookie:
            print("✅ Single account refresh successful!")
            print(f"Cookie length: {len(cookie)} characters")
            print(f"Cookie preview: {cookie[:100]}...")
        else:
            print("❌ Single account refresh failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")


async def test_batch_refresh():
    """Test refreshing multiple accounts."""
    print("\nTesting batch account refresh...")
    
    # Replace with real credentials for testing
    accounts = [
        ("<EMAIL>", "user1", "pass1"),
        ("<EMAIL>", "user2", "pass2"),
    ]
    
    try:
        await refresh_cookies_camoufox(accounts)
        print("✅ Batch refresh completed!")
        
    except Exception as e:
        print(f"❌ Batch refresh error: {e}")


async def test_update_system():
    """Test the automatic update system."""
    print("\nTesting automatic update system...")
    
    try:
        await update_cookies_camoufox()
        print("✅ Automatic update completed!")
        
    except Exception as e:
        print(f"❌ Automatic update error: {e}")


async def main():
    """Main test function."""
    print("Cookie Refresh Camoufox Test")
    print("=" * 40)
    print()
    
    print("This script tests the Camoufox-only cookie refresh functionality.")
    print("Make sure to:")
    print("1. Install Camoufox: pip install camoufox")
    print("2. Update the credentials in this script")
    print("3. Ensure you have access to the proxy system")
    print("4. Configure database connection")
    print()
    
    # Run tests
    await test_single_account()
    await test_batch_refresh()
    await test_update_system()
    
    print("\nAll tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
