import query_builder as qb
import pandas as pd

steam_public = qb.Schema('public', db_name='steam')
steam_player = qb.Schema('player', db_name='steam')

async def get_steam_ids():
    qb.APIQuery(steam_player.player_base).copy_to_csv("scrapbook/player-base.tsv")
    player_base = pd.read_csv("scrapbook/player-base.tsv", sep="\t")
    active = pd.read_csv("scrapbook/inactive/steam-active-users.tsv")

    result = active.merge(
        player_base[["pk", "steam_id"]], how="left", left_on="user_id", right_on="pk"
    ).drop(columns=["pk"])

    result.to_csv("scrapbook/steam_library/active_steam_ids2.tsv", sep="\t", index=False)

async def run_main():
    await get_steam_ids()

if __name__ == "__main__":
    import asyncio
    asyncio.run(run_main())