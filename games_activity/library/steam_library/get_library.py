import xml.etree.ElementTree as ET
import asyncio
from asyncio import Queue, QueueEmpty
from datetime import datetime, timedelta
import pytz
import random
import time
import aiohttp
from library.steam_library.queries.sldb import sldb_api
from library.steam_library.cookie_refresh_camoufox import refresh_cookies_camoufox
from library.steam_library.proxies import get_proxies
from library.steam_library.steam_users import get_all_ids
import query_builder as qb

libraries = qb.Schema('sldb', db_name='steam_test')
steam_libraries = qb.Schema('library', db_name='steam')

PROXY_TIMEOUT = 4
HEADERS = {
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-encoding": "gzip, deflate, br, zstd",
    "accept-language": "en-US,en;q=0.9",
    "connection": "keep-alive",
    "host": "steamcommunity.com",
    "sec-ch-ua-platform": "Windows",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "none",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

async def update_cookies():
    now = datetime.now(tz=pytz.UTC)
    # cookies = await sldb_api.account_cookies.fetchall()
    cookies = await qb.Query(steam_libraries.account_cookies).fetchall()
    need_updating = []
    for c in cookies:
        if c['last_updated'] < now - timedelta(hours=PROXY_TIMEOUT):
            need_updating.append(c['account_pk'])
    account_list = []
    for id in need_updating:
        account_info = []
        # a = await sldb_api.account_details.where(id = id).fetchone()
        a = await qb.Query(steam_libraries.account_details).where(id = id).fetchone()
        account_info.append(a['email'])
        account_info.append(a['username'])
        account_info.append(a['password'])
        account_list.append(account_info)
    print("updating cookies")
    await refresh_cookies_camoufox(account_list)

async def get_cookies(id_max=40):
    await update_cookies()
    now = datetime.now(tz=pytz.UTC)
    # account_cookies = await sldb_api.account_cookies.fetchall()
    account_cookies = await qb.Query(steam_libraries.account_cookies).fetchall()
    cookies = []
    for c in account_cookies:
        if c['last_updated'] > now - timedelta(hours=PROXY_TIMEOUT):
            if c['account_pk'] >id_max:
                continue
            cookies.append(await format_cookies(c['cookie']))
    return cookies

async def format_cookies(cookie:list):
    pairs = cookie.split(";")
    cookie_dict = {}
    for pair in pairs:
        key, value = pair.split("=")
        cookie_dict[key.strip()] = value.strip('"')
    return (cookie_dict)


async def parse_games_response(resp: aiohttp.ClientResponse):
    try:
        root = ET.fromstring(await resp.text())
        games = []
        for game in root.findall(".//games/game"):
            game_info = {
                "appID": game.find("appID").text if game.find("appID") is not None else None,
                "name": game.find("name").text if game.find("name") is not None else None,
                "hoursOnRecord": game.find("hoursOnRecord").text if game.find("hoursOnRecord") is not None else None,
            }
            games.append(game_info)

        if games:
            return games
        else:
            return []
    except ET.ParseError as e:
        print(f"Failed to parse XML: {e}")
        

async def get_games(pid, cookies, proxies):
    proxy = random.choice(proxies)
    p = f"http://{proxy['username']}:{proxy['password']}@{proxy['address']}:{proxy['port']}/"
    url = f"https://steamcommunity.com/profiles/{pid}/games/?tab=all&xml=1"
    count = 1
    while True:
        if count > 4:
            # print(f'{pid} took too long, returning empty')
            return []
        resp = 0
        timeout = aiohttp.ClientTimeout(total=8*count)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                resp = await session.get(url, headers=HEADERS, cookies=cookies, proxy=p)
            except Exception as e:
                # print(e)
                pass
                # _=input()
            if not resp:
                proxy = random.choice(proxies)
                p = f"http://{proxy['username']}:{proxy['password']}@{proxy['address']}:{proxy['port']}/"
                count += 1
                continue
            elif resp.status == 200:
                # print(f"Page fetched successfully!")
                return await parse_games_response(resp)
            elif resp.status == 500:
                # print("suspicous account, skipping")
                return []
            else:
                print(f"Failed to fetch page. Status code: {resp.status}")

async def format_games(pid, games)->list[dict]:
    player_games = []
    current_time = datetime.now(tz=pytz.utc)
    if games:
        for game in games:
            playtime = str(game['hoursOnRecord'])
            if game['hoursOnRecord'] == None:
                playtime = "0.0"
            info = {
                "pid": pid,
                "game_id": game["appID"],
                "play_time": playtime.replace(',', ''),
                "first_seen": current_time,
            }
            player_games.append(info)
        return player_games


async def insert_games(result_queue:Queue, formatted_games:list[dict], seen_queue:Queue):
    now = datetime.now(tz=pytz.utc)
    if not formatted_games:
        return
    for fg in formatted_games:
        await result_queue.put(fg)
    if result_queue.qsize() < 250:
        return
    to_insert = []
    while not result_queue.empty():
        to_insert.append(result_queue.get_nowait())
    # await sldb_api.user_games.insert(to_insert)
    await insert_user_games(to_insert)

    to_update=[]
    while not seen_queue.empty():
        to_update.append({'steam_id': seen_queue.get_nowait(), 'last_updated': now})

    if len(to_update) > 0:
        await insert_active_profiles(to_update)

async def insert_user_games(to_insert:list):
    insert_stmt = qb.insert(steam_libraries.user_games).values(to_insert)
    insert_stmt = (
            insert_stmt
            .on_conflict_do_update(
                index_elements=[steam_libraries.user_games.c.pid, steam_libraries.user_games.c.game_id],
                set_={ 'play_time': insert_stmt.excluded.play_time }
                )
        )
    await qb.engine(insert_stmt, db_name='steam').execute()

async def insert_active_profiles(to_update:list):
    insert_stmt = qb.insert(steam_libraries.active_profiles).values(to_update)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_update(
            index_elements=[steam_libraries.active_profiles.c.steam_id],
            set_={ 'last_updated': insert_stmt.excluded.last_updated }
            )
    )
    await qb.engine(insert_stmt, db_name='steam').execute()
    
async def process(account_queue:Queue, result_queue:Queue, seen_queue:Queue, cookie, proxies):
    while True:
        try:
            pid = account_queue.get_nowait()
        except QueueEmpty:
            break
        while True:
            try:
                t0 = time.time()
                games = await get_games(pid, cookie, proxies)
                formatted_games = await format_games(pid, games)
                seen_queue.put_nowait(pid)
                await insert_games(result_queue, formatted_games, seen_queue)
                t1 = time.time()-t0
                if t1 > 100:
                    print(f'account {pid} took {t1}')
                break
            except asyncio.TimeoutError:
                print("timeout")
                continue
            except Exception as e:
                print(e)
                continue

async def main(steam_ids, proxies, cookies):
    account_queue = Queue()
    for pid in steam_ids:
        account_queue.put_nowait(pid)

    result_queue = Queue()
    seen_queue = Queue()
    start_time = time.time()

    await asyncio.gather(*[process(account_queue, result_queue, seen_queue, cookie, proxies) for cookie in cookies])
    to_insert=[]
    while not result_queue.empty():
        to_insert.append(result_queue.get_nowait())
    if len(to_insert)>0:
        await insert_user_games(to_insert)
    to_update=[]
    now = datetime.now(tz=pytz.utc)
    while not seen_queue.empty():
        to_update.append({'steam_id': seen_queue.get_nowait(), 'last_updated': now})
    if len(to_update) > 0:
        await insert_active_profiles(to_update)
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    print(f"Batch completed in {elapsed_time:.2f} seconds.")
    return elapsed_time

async def run_main():
    print("Starting")
    await asyncio.sleep(5)
    print("Getting proxies")
    proxies = await get_proxies()
    print("Getting cookies")
    cookies = await get_cookies()
    print(f"Running with {len(cookies)} cookies")
    print(f"Running with {len(proxies)} proxies")
    # steam_ids = await get_all_ids()
    # print(steam_ids[:5])
    all_steam_ids = qb.APIQuery(steam_libraries.active_profiles).order_by("last_updated").select("steam_id").fetchall_sync()
    steam_ids = [item["steam_id"] for item in all_steam_ids]

    batch_size = 30000

    total_time = 0
    total_processed = 0

    for i in range(0, len(steam_ids), batch_size):
        print(f"Starting batch {i + batch_size} for session")
        sids = steam_ids[i:i + batch_size]
        total_time += await main(sids, proxies, cookies)
        total_processed += batch_size
        print("-----------------------------------------")
        print(f"Processed {total_processed} in {total_time:.2f} seconds")
        print(f"{total_processed/total_time:.2f} accounts processed per second")
        print(f"{total_time/total_processed:.2f} seconds per account")
        print("-----------------------------------------")

if __name__ == "__main__":
    print("------------")
    print("start")
    import time
    t0 = time.time()
    asyncio.run(run_main())
    print(f'took {time.time()-t0}s')
