import query_builder as qb
from datetime import datetime, timedelta
import pytz
import time
from library.steam_library.queries.sldb import sldb_api
import query_builder as qb

libraries = qb.Schema('sldb', db_name='steam_test')
TIME_DELAY = 1


async def get_game_names(game_list):
    name_list = []
    for g in game_list:
        name = await sldb_api.game_base.where(qb.c_.game_id == g).fetchone()
        if name:
            name_list.append(name['game_name'])
        else:
            name_list.append('Not in game list')
    return name_list

'''
takes a steam_id and returns the first time the profile was seen in the database
'''
async def get_account_first_seen(pid:int)->datetime:
    earliest_date = datetime.now(tz=pytz.utc)

    library = await sldb_api.user_games.where(qb.c_.pid == int(pid)).fetchall()
    
    for l in library:
        if l['first_seen'] < earliest_date:
            earliest_date = l['first_seen']

    return earliest_date

'''
takes a list of game_ids, minimum number of hours you want them to have been played,
and how many of the games you want them to have own (default to all).
Returns the number of players in the panel who match your input
'''
async def shared_games(games:list, min_hours=10, min_matches=-1)->int:
    if not (min_matches > 0 and min_matches < len(games)):
        min_matches = len(games)
    
    query = (
        sldb_api.user_games.select('pid')
            .where(qb.c_.game_id.in_(games), qb.c_.play_time >= min_hours)
            .group_by(qb.c_.pid)
            .having(qb.func.count(qb.c_.game_id.distinct()) >= min_matches)
    )
    steam_ids = await query.fetchall()
    return len(steam_ids)


'''
takes a list of game_ids the profiles must have, a list of game_ids they can have,
minimum number of hours you want them to have been played,
and how many of the games you want them to have own (default to all).
Returns the number of players in the panel who match your input
'''
async def filtered_shared_games(must_have_games: list, match_games: list, min_hours=10, min_matches=-1) -> int:
    if not (min_matches > 0 and min_matches < len(match_games)):
        min_matches = len(match_games)

    must_have_subquery = (
        sldb_api.user_games.select(qb.c_.pid)
        .where(qb.c_.game_id.in_(must_have_games), qb.c_.play_time >= min_hours)
        .group_by(qb.c_.pid)
        .having(qb.func.count(qb.c_.game_id.distinct()) == len(must_have_games))
        .cte()
    )

    query = (
        sldb_api.user_games.select('pid')
        .where(qb.c_.pid.in_(must_have_subquery), qb.c_.game_id.in_(match_games), qb.c_.play_time >= min_hours)
        .group_by(qb.c_.pid)
        .having(qb.func.count(qb.c_.game_id.distinct()) >= min_matches)
    )

    steam_ids = await query.fetchall()
    return len(steam_ids)

'''
takes a list of game_ids the profiles must have, a list of game_ids they can have,
minimum number of hours you want them to have been played,
and how many of the games you want them to have own (default to all).
return the percent of players who have played the must_have_games that have also played 
at least min_matches of match_games
'''
async def percent_given_played(must_have_games: list, match_games: list, min_hours=10, min_matches=-1)->float:
    if not (min_matches > 0 and min_matches < len(match_games)):
        min_matches = len(match_games)
    must_have_len = await shared_games(must_have_games, min_hours=min_hours)
    given_len = await filtered_shared_games(must_have_games, match_games, min_hours=min_hours, min_matches=min_matches)
    percent = 100 * given_len / must_have_len
    return percent

'''
returns the number of unique profiles in the database
'''
async def get_panel_size():
    query = (
        sldb_api.active_profiles.select(qb.func.count(qb.func.distinct(sldb_api.active_profiles.c.steam_id)))
    )
    panel_size = await query.fetchall()
    return panel_size[0]['count_1']

async def percent_old_unplayed_games(days_ago=14) -> float:
    time_difference = datetime.now(tz=pytz.utc) - timedelta(days=days_ago)

    total_old_games_query = (
        sldb_api.user_games.select(qb.func.count())
        .where(qb.c_.first_seen <= time_difference)
    )
    total_old_games = (await total_old_games_query.fetchone())["count_1"]

    if total_old_games == 0:
        return 0.0

    unplayed_old_games_query = (
        sldb_api.user_games.select(qb.func.count())
        .where(qb.c_.first_seen <= time_difference, qb.c_.play_time == 0.0)
    )
    unplayed_old_games = (await unplayed_old_games_query.fetchone())["count_1"]

    return (unplayed_old_games / total_old_games) * 100

'''
returns a dictionary of all new games from the past day and the count of how many have been seen
also returns the total count of new games
'''
async def new_games_daily(time_delay=TIME_DELAY)->dict:
    current_date = datetime.now(tz=pytz.utc)
    last_date = current_date - timedelta(days=time_delay)
    new_games = await sldb_api.user_games.where(qb.func.date_trunc('day', qb.c_.first_seen) > last_date).fetchall()
    game_dict = {}
    count = 0
    for game in new_games:
        g = game['game_id']
        game_dict[g] = game_dict.get(g, 0) + 1
        count += 1
    
    return game_dict, count


async def top_related_games(game_list: list, min_hours=10):
    have_len = await shared_games(game_list, min_hours=min_hours)
    games = await related_games(game_list, min_hours=min_hours)
    print(f"{have_len} accounts have played {game_list}")
    for game in games:
        id, count = game
        print(f"{id}: {count} - {100*count/have_len:.2f}%")

"""
Given a list of game IDs, returns the top `limit` games that users who played those games have also played,
ranked by the number of players.
"""
async def related_games(game_list: list, limit: int = 20, min_hours=1) -> list:
    user_subquery = (
        sldb_api.user_games.select(qb.c_.pid)
        .where(qb.c_.game_id.in_(game_list), qb.c_.play_time >= min_hours)
        .group_by(qb.c_.pid)
        .having(qb.func.count(qb.c_.game_id.distinct()) == len(game_list))
        .cte()
    )

    query = (
        sldb_api.user_games.select(qb.c_.game_id, qb.func.count(qb.c_.pid).label("player_count"))
        .where(qb.c_.pid.in_(user_subquery), ~qb.c_.game_id.in_(game_list), qb.c_.play_time >= min_hours)
        .group_by(qb.c_.game_id)
        .order_by(qb.desc("player_count"))
        .limit(limit)
    )

    top_games = await query.fetchall()
    return [(game["game_id"], game["player_count"]) for game in top_games]

"""
Given a list of must-have games and other games, returns the average number of other games
owned by accounts that have all of the must-have games.
"""
async def avg_other_games_owned(must_have_games: list, other_games: list, min_hours=1) -> float:
    if not must_have_games or not other_games:
        return 0.0

    user_subquery = (
        sldb_api.user_games.select(qb.c_.pid)
        .where(qb.c_.game_id.in_(must_have_games), qb.c_.play_time >= min_hours)
        .group_by(qb.c_.pid)
        .having(qb.func.count(qb.c_.game_id.distinct()) == len(must_have_games))
        .cte()
    )

    query = (
        sldb_api.user_games.select(qb.c_.pid, qb.func.count(qb.c_.game_id).label("other_game_count"))
        .where(qb.c_.pid.in_(user_subquery), qb.c_.game_id.in_(other_games), qb.c_.play_time >= min_hours)
        .group_by(qb.c_.pid)
    )

    user_game_counts = await query.fetchall()
    
    if not user_game_counts:
        return 0.0

    total_games = sum(user["other_game_count"] for user in user_game_counts)
    avg_games = total_games / len(user_game_counts)
    
    return avg_games

'''
returns a dictionary of all new games that have 0 hours played 
from the past day and the count of how many have been seen
also returns the total count of new games
'''
async def new_zero_games()->dict:
    current_date = datetime.now(tz=pytz.utc)
    last_date = current_date - timedelta(days=TIME_DELAY)
    new_games = await sldb_api.user_games.where(qb.c_.first_seen > last_date).where(qb.c_.play_time == 0.0).fetchall()
    game_dict = {}
    count = 0
    for game in new_games:
        g = game['game_id']
        game_dict[g] = game_dict.get(g, 0) + 1
        count += 1
    
    return game_dict, count

'''
given a game_id, returns how many times it is seen as a new game in the past day
'''
async def new_game_count(game_id:int)->int:
    current_date = datetime.now(tz=pytz.utc)
    last_date = current_date - timedelta(days=TIME_DELAY)
    new_games = await sldb_api.user_games.where(qb.c_.game_id==game_id).where(qb.c_.first_seen > last_date).fetchall()
    return len(new_games)

'''
given a game_id, returns how many times it is seen as a new game and has 0 hours played in the past day
'''
async def zero_new_game_count(game_id:int)->int:
    current_date = datetime.now(tz=pytz.utc)
    last_date = current_date - timedelta(days=TIME_DELAY)
    new_games = await sldb_api.user_games.where(qb.c_.game_id==game_id).where(play_time=0.0).where(qb.c_.first_seen > last_date).fetchall()
    return len(new_games)

async def menu(number, must_have_games=[], games_list=[], min_hours=0, min_matches=-1, time_delay=TIME_DELAY):
    must_have_game_names = await get_game_names(must_have_games)
    game_list_names = await get_game_names(games_list)
    match number:
        case 1:
            print("------------------")
            print(f"Getting Panel Size")
            print(f"Panel Size: {await get_panel_size()}")
        case 2:
            print("------------------")
            print(f"Getting most played games given account has played {game_list_names}")
            await top_related_games(games_list, min_hours=min_hours)
        case 3:
            print("------------------")
            print(f"Getting average number of {game_list_names} played given account has played {must_have_game_names}")
            print(f"Average: {await avg_other_games_owned(must_have_games=must_have_games, other_games=games_list, min_hours=min_hours):.2f}")
        case 4:
            print("------------------")
            print(f"Getting the percent of players who have played at least {min_matches} of {game_list_names} given they've played {must_have_game_names}")
            percent = await percent_given_played(must_have_games, games_list, min_hours=min_hours, min_matches=min_matches)
            print(f"{percent}% of people who have played {must_have_game_names} have played at least {min_matches} of {game_list_names}")
        case 5:
            print("------------------")
            print(f"Getting top new games from the past {time_delay} day(s)")
            new_game_dict, new_count = await new_games_daily(time_delay=time_delay)
            print(f"{new_count} new games found")
            sorted_games = sorted(new_game_dict.items(), key=lambda item: item[1], reverse=True)[:20]
            for game, count in sorted_games:
                print(f"{game}: {count}")

async def run():
    print("Starting")
    start_time = time.time()
    # await menu(1)
    # await menu(2, games_list=[1649080])
    await menu(3, must_have_games=[535930], games_list=[2185060, 1649080])
    await menu(4, must_have_games=[535930], games_list=[2185060, 1649080], min_matches=1)
    await menu(4, must_have_games=[535930], games_list=[2185060, 1649080], min_matches=2)
    # await menu(5, time_delay=3)

    # await menu(4, must_have_games=[1245620], games_list=[570940, 236430, 374320, 814380], min_matches=4)

    end_time = time.time()
    elapsed_time = end_time - start_time
    print(f"Completed in {elapsed_time:.2f} seconds.")

if __name__ == '__main__':
    import asyncio
    asyncio.run(run())