import query_builder as qb
import pytz
from datetime import datetime, timedelta

libraries = qb.Schema('sldb', db_name='steam_test')

async def get_users():
    users = qb.APIQuery(libraries.active_profiles).order_by("last_updated").select("steam_id").fetchall_sync()
    print(users[0])
    print(users[-1])
    print(len(users))

async def run_main():
    await get_users()



if __name__ == '__main__':
    import asyncio
    asyncio.run(run_main())