import query_builder as qb

class SldbSchema(qb.Schema):
    _db_name = 'steam_test'
    _schema = 'sldb'

    class user_games(qb.Table):
        _columns = [
            qb.ID(),
            qb.TableColumn('pid', qb.BIGINT),
            qb.TableColumn('game_id', qb.BIGINT),
            qb.TableColumn('play_time', qb.FLOAT),
            qb.TableColumn('first_seen', qb.TIMESTAMPTZ),
        ]
        _constraints = [qb.UniqueConstraint('pid', 'game_id')]
        #_csv_opts = qb.CopyFromOpts(file_path='scrapbook/steam_library/library_data.tsv')

    class account_details(qb.Table):
        _columns = [
            qb.ID(),
            qb.TableColumn('email', qb.TEXT),
            qb.TableColumn('username', qb.TEXT),
            qb.TableColumn('password', qb.TEXT),
        ]
        # _csv_opts = qb.CopyFromOpts(file_path='scrapbook/steam_library/account_data.tsv')

    class account_cookies(qb.Table):
        _columns = [
            qb.ID(),
            qb.TableColumn('account_pk', qb.INTEGER),
            qb.TableColumn('cookie', qb.TEXT),
            qb.TableColumn('last_updated', qb.TIMESTAMPTZ),
        ]
        # _csv_opts = qb.CopyFromOpts(file_path='scrapbook/steam_library/cookies.tsv')

    class active_profiles(qb.Table):
        _columns = [
            qb.ID(),
            qb.TableColumn('steam_id', qb.BIGINT),
            qb.TableColumn('last_updated', qb.TIMESTAMPTZ),
        ]
        _constraints = [qb.UniqueConstraint('steam_id')]
        # _csv_opts = qb.CopyFromOpts(file_path='scrapbook/steam_library/active_steam_ids2.tsv')

    class game_base(qb.Table):
        _columns = [
            qb.ID(),
            qb.TableColumn('game_id', qb.BIGINT),
            qb.TableColumn('game_name', qb.TEXT),
            qb.TableColumn('full_game_id', qb.BIGINT, nullable = True),
        ]
        _constraints = [qb.UniqueConstraint('game_id')]
        # _csv_opts = qb.CopyFromOpts(file_path='library/steam_library/game_base.csv')

sldb = SldbSchema()