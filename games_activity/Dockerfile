FROM python:3.11-slim

RUN apt-get update && apt-get install -y --no-install-recommends build-essential git

RUN apt-get install -y xvfb

WORKDIR /home/<USER>

COPY ./requirements.txt ./
RUN  pip install --no-cache-dir -r requirements.txt
RUN python -m playwright install
RUN playwright install-deps
RUN camoufox fetch

ENV BEZIER_NO_EXTENSION=1

COPY ./ ./
COPY config-docker.py config.py
COPY db.yaml qb-dbs.yaml

CMD ["python"]
