import query_builder as qb
import asyncio
from datetime import datetime, timedelta
import pandas as pd
from query_builder import func, literal, text
import pytz
import time

discord_db = qb.db(db_name='discord')
discord_public = qb.Schema('public', db_name='discord')

# Cache dictionaries for lookups
_act_state_cache = {}
_act_details_cache = {}
_guild_cache = {}
_game_cache = {}
_state_cache = {}
_presence_cache = {}

async def game_insert(games:list[str]):
    formatted_games = [{'game_name': game} for game in games]
    insert_stmt = qb.insert(discord_public.game_base).values(formatted_games)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_nothing(
            index_elements=[discord_public.game_base.c.game_name],
            )
    )
    await qb.engine(insert_stmt, db_name='discord').execute()

async def act_state_insert(act_states:list[str]):
    formatted_states = [{'act_state': state} for state in act_states]
    insert_stmt = qb.insert(discord_public.act_state_base).values(formatted_states)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_nothing(
            index_elements=[discord_public.act_state_base.c.act_state],
            )
    )
    await qb.engine(insert_stmt, db_name='discord').execute()

async def act_details_insert(act_details:list[str]):
    formatted_details = [{'act_details': detail} for detail in act_details]
    insert_stmt = qb.insert(discord_public.act_details_base).values(formatted_details)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_nothing(
            index_elements=[discord_public.act_details_base.c.act_details],
            )
    )
    await qb.engine(insert_stmt, db_name='discord').execute()
    
async def state_insert(states:list[(int, int)]):
    formatted_states = []
    for state in states:
        act_state_name, act_details_name = state
        act_state_id = await get_act_state_id(act_state_name)
        act_details_id = await get_act_details_id(act_details_name)
        formatted_states.append({'act_state_id': act_state_id, 'act_details_id': act_details_id})

    insert_stmt = qb.insert(discord_public.state_base).values(formatted_states)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_nothing(
            index_elements=[discord_public.state_base.c.act_state_id, discord_public.state_base.c.act_details_id],
            )
    )
    await qb.engine(insert_stmt, db_name='discord').execute()

async def guild_insert(guilds:list[str]):
    formatted_guilds = [{'guild_name': guild} for guild in guilds]
    insert_stmt = qb.insert(discord_public.guild_base).values(formatted_guilds)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_nothing(
            index_elements=[discord_public.guild_base.c.guild_name],
            )
    )
    await qb.engine(insert_stmt, db_name='discord').execute()

async def player_insert(players:list[str]):
    formatted_players = []
    for p in players:
        first_seen = datetime.now(tz=pytz.UTC)
        formatted_players.append({'user_id': p, 'first_seen': first_seen})
    insert_stmt = qb.insert(discord_public.player_base).values(formatted_players)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_nothing(
            index_elements=[discord_public.player_base.c.user_id],
            )
    )
    await qb.engine(insert_stmt, db_name='discord').execute()

async def player_guild_map_insert(player_guild_map:list[(int, str)]):
    formatted_player_guild_map = []
    for user_id, guild_name in player_guild_map:
        first_seen = datetime.now(tz=pytz.UTC)
        last_seen = datetime.now(tz=pytz.UTC)
        guild_id = await get_guild_id(guild_name)
        formatted_player_guild_map.append({'user_id': user_id, 'guild_id': guild_id, 'first_seen': first_seen, 'last_seen': last_seen})
    
    insert_stmt = qb.insert(discord_public.player_guild_map).values(formatted_player_guild_map)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_update(
            index_elements=[discord_public.player_guild_map.c.user_id, discord_public.player_guild_map.c.guild_id],
            set_={ 'last_seen': insert_stmt.excluded.last_seen }
            )
    )
    await qb.engine(insert_stmt, db_name='discord').execute()

async def precence_insert(presences: list[dict[str, any]]):
    formatted_presences = []
    day = datetime.now(tz=pytz.UTC).date()
    current_time = datetime.now(tz=pytz.UTC)

    for p in presences:
        game_id = await get_game_id(p['game_name'])
        state_id = await get_state_id(p['act_state_name'], p['act_details_name'])
        formatted_presences.append({
            'player_id': p['player_id'],
            'game_id': game_id,
            'state_id': state_id,
            'time_window': p['time_window'],
            'hits': 1,
            'day': day
        })

    insert_stmt = qb.insert(discord_public.presence_test).values(formatted_presences)

    insert_stmt = insert_stmt.on_conflict_do_update(
        index_elements=[
            discord_public.presence_test.c.player_id,
            discord_public.presence_test.c.game_id,
            discord_public.presence_test.c.state_id,
            discord_public.presence_test.c.time_window,
            discord_public.presence_test.c.day
        ],
        set_={
            'hits': discord_public.presence_test.c.hits + 1,
        }
    )

    await qb.engine(insert_stmt, db_name='discord').execute()

    for p in presences:
        presence_id = await get_presence_id(p['player_id'], game_id, state_id, p['time_window'], day)
        await presence_records_insert(presence_id, current_time)

async def presence_records_insert(presence_id: int, time_stamp: datetime):
    insert_stmt = (
        qb.insert(discord_public.presence_records_test)
        .values(presence_id=presence_id, timestamp=time_stamp)
    )
    await qb.engine(insert_stmt, db_name='discord').execute()

async def get_presence_id(player_id: int, game_id: int, state_id: int, time_window: int, day: datetime.date) -> int:
    cache_key = (player_id, game_id, state_id, time_window, day)
    if cache_key in _presence_cache:
        return _presence_cache[cache_key]

    query = (
        qb.Query(discord_public.presence_test)
        .select(discord_public.presence_test.c.presence_id)
        .where(discord_public.presence_test.c.player_id == player_id)
        .where(discord_public.presence_test.c.game_id == game_id)
        .where(discord_public.presence_test.c.state_id == state_id)
        .where(discord_public.presence_test.c.time_window == time_window)
        .where(discord_public.presence_test.c.day == day)
    )
    result = await query.fetchall()
    presence_id = result[0]['presence_id']
    _presence_cache[cache_key] = presence_id
    return presence_id

async def get_act_state_id(act_state:str) -> int:
    if act_state in _act_state_cache:
        return _act_state_cache[act_state]

    query = (
        qb.Query(discord_public.act_state_base)
        .select(discord_public.act_state_base.c.act_state_id)
        .where(discord_public.act_state_base.c.act_state == act_state)
    )
    result = await query.fetchall()
    act_state_id = result[0]['act_state_id']
    _act_state_cache[act_state] = act_state_id
    return act_state_id

async def get_act_details_id(act_details:str) -> int:
    if act_details in _act_details_cache:
        return _act_details_cache[act_details]

    query = (
        qb.Query(discord_public.act_details_base)
        .select(discord_public.act_details_base.c.act_details_id)
        .where(discord_public.act_details_base.c.act_details == act_details)
    )
    result = await query.fetchall()
    act_details_id = result[0]['act_details_id']
    _act_details_cache[act_details] = act_details_id
    return act_details_id

async def get_guild_id(guild_name:str) -> int:
    if guild_name in _guild_cache:
        return _guild_cache[guild_name]

    query = (
        qb.Query(discord_public.guild_base)
        .select(discord_public.guild_base.c.guild_id)
        .where(discord_public.guild_base.c.guild_name == guild_name)
    )
    result = await query.fetchall()
    guild_id = result[0]['guild_id']
    _guild_cache[guild_name] = guild_id
    return guild_id

async def get_game_id(game_name: str) -> int:
    if game_name in _game_cache:
        return _game_cache[game_name]

    query = (
        qb.Query(discord_public.game_base)
        .select(discord_public.game_base.c.game_id)
        .where(discord_public.game_base.c.game_name == game_name)
    )
    result = await query.fetchall()
    game_id = result[0]['game_id']
    _game_cache[game_name] = game_id
    return game_id

async def get_state_id(act_state_name: str, act_details_name: str) -> int:
    cache_key = (act_state_name, act_details_name)
    if cache_key in _state_cache:
        return _state_cache[cache_key]

    act_state_id = await get_act_state_id(act_state_name)
    act_details_id = await get_act_details_id(act_details_name)
    query = (
        qb.Query(discord_public.state_base)
        .select(discord_public.state_base.c.state_id)
        .where(discord_public.state_base.c.act_state_id == act_state_id)
        .where(discord_public.state_base.c.act_details_id == act_details_id)
    )
    result = await query.fetchall()
    state_id = result[0]['state_id']
    _state_cache[cache_key] = state_id
    return state_id

async def load_data():
    path = 'test_outputs/lol.csv'
    df = pd.read_csv(path)
    return df

async def format_data(df: pd.DataFrame):
    data = []
    for index, row in df.iterrows():
        data.append({
            'player_id': row['User_ID'],
            'game_name': row['Act_Name'],
            'act_state_name': row['Act_State'],
            'act_details_name': row['Act_Details'],
            'time_window': 0,
            'guild_name': row['Guild_Name']
        })
    return data

async def insert_data(data: list[dict[str, any]]):
    for d in data:
        await game_insert([d['game_name']])
        await act_state_insert([d['act_state_name']])
        await act_details_insert([d['act_details_name']])
        await state_insert([(d['act_state_name'], d['act_details_name'])])
        await guild_insert([d['guild_name']])
        await player_insert([d['player_id']])
        await player_guild_map_insert([(d['player_id'], d['guild_name'])])
        await precence_insert([d])

async def main():

    df = await load_data()
    data = await format_data(df)
    await insert_data(data)
    

if __name__ == "__main__":
    asyncio.run(main())