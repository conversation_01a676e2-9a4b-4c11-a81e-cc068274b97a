import query_builder as qb
import asyncio

discord_db = qb.db(db_name='discord')
discord_public = qb.Schema('public', db_name='discord')

async def game_insert(games:list[dict[str, str]]):
    insert_stmt = qb.insert(discord_public.game_base).values(games)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_nothing(
            index_elements=[discord_public.game_base.c.game_name],
            )
    )
    await qb.engine(insert_stmt, db_name='discord').execute()

async def state_insert(states:list[dict[str, str]]):
    insert_stmt = qb.insert(discord_public.state_base).values(states)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_nothing(
            index_elements=[discord_public.state_base.c.state_name],
            )
    )
    await qb.engine(insert_stmt, db_name='discord').execute()

async def details_insert(details:list[dict[str, str]]):
    insert_stmt = qb.insert(discord_public.details_base).values(details)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_nothing(
            index_elements=[discord_public.details_base.c.details_name],
            )
    )
    await qb.engine(insert_stmt, db_name='discord').execute()

if __name__ == "__main__":
    asyncio.run(game_insert())