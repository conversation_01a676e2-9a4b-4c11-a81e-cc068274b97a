# Discord Presence Analysis Tool

A clean, extensible Python tool for analyzing Discord presence data from CSV files. This refactored version provides a much more organized and maintainable approach to data analysis.

## Features

### Core Functionality
- **Data Loading**: Robust CSV loading with validation and error handling
- **Filtering**: Chainable filtering methods for flexible data manipulation
- **Analysis**: Built-in methods for common analysis tasks
- **Export**: Save filtered data and analysis results to CSV files
- **Logging**: Comprehensive logging for debugging and monitoring

### Key Improvements Over Original
- ✅ **Object-oriented design** with proper class structure
- ✅ **Method chaining** for fluent API usage
- ✅ **Type hints** for better code documentation and IDE support
- ✅ **Error handling** with informative error messages
- ✅ **Logging** for better debugging and monitoring
- ✅ **Extensibility** - easy to add new analysis functions
- ✅ **No unnecessary async/await** - simplified synchronous operations
- ✅ **Comprehensive documentation** with docstrings

## Quick Start

```python
from output_analysis import DiscordPresenceAnalyzer

# Initialize and load data
analyzer = DiscordPresenceAnalyzer("output_test.csv")
analyzer.load_data()

# Chain filters for specific analysis
analyzer.filter_bots(exclude_bots=True) \
        .filter_by_status('online') \
        .filter_non_null('Act_Name')

# Get analysis results
top_activities = analyzer.get_value_counts('Act_Name')
print(top_activities.head(10))

# Save results
analyzer.save_value_counts('Act_Name', 'top_activities.csv')
```

## Main Class: DiscordPresenceAnalyzer

### Initialization
```python
analyzer = DiscordPresenceAnalyzer("path/to/your/data.csv")
```

### Data Loading
```python
analyzer.load_data()  # Load CSV data
info = analyzer.get_data_info()  # Get dataset information
```

### Filtering Methods (Chainable)
```python
# Filter out bots
analyzer.filter_bots(exclude_bots=True)

# Filter by status
analyzer.filter_by_status('online')

# Filter by activity type
analyzer.filter_by_activity_type(4, exclude=True)

# Remove null values
analyzer.filter_non_null('Act_Name')

# Custom column filtering
analyzer.filter_by_column('Platform', 'desktop')

# Reset all filters
analyzer.reset_filters()
```

### Analysis Methods
```python
# Get unique values
unique_activities = analyzer.get_unique_values('Act_Name')

# Get value counts
activity_counts = analyzer.get_value_counts('Act_Name')

# Comprehensive summaries
activity_summary = analyzer.get_activity_summary()
guild_analysis = analyzer.get_guild_analysis()
```

### Display Methods
```python
# Print data summary
analyzer.print_summary(columns=['User_ID', 'Act_Name'], head=10)

# Print unique values
analyzer.print_unique_values('Platform')

# Print value counts
analyzer.print_value_counts('Act_Name', top_n=10)
```

### Export Methods
```python
# Save current filtered data
analyzer.save_data('filtered_results.csv')

# Save value counts
analyzer.save_value_counts('Act_Name', 'activity_counts.csv')
```

## Convenience Functions

### Quick Analysis
```python
from output_analysis import quick_analysis

results = quick_analysis("output_test.csv")
print(results['activity_summary'])
```

### Activity Name Analysis
```python
from output_analysis import analyze_activity_names

activity_counts = analyze_activity_names(
    filepath="output_test.csv",
    exclude_bots=True,
    output_file="activity_analysis.csv"
)
```

## Extending the Analyzer

The refactored design makes it easy to add new analysis functions:

```python
class MyCustomAnalyzer(DiscordPresenceAnalyzer):
    def analyze_custom_metric(self):
        """Add your custom analysis here."""
        if self.df is None:
            raise ValueError("No data loaded. Call load_data() first.")
        
        # Your custom analysis logic
        result = self.df.groupby('Guild_Name')['User_ID'].nunique()
        return result.to_dict()

# Usage
custom_analyzer = MyCustomAnalyzer("data.csv")
custom_analyzer.load_data()
results = custom_analyzer.analyze_custom_metric()
```

## Example Workflows

### Basic Activity Analysis
```python
analyzer = DiscordPresenceAnalyzer("output_test.csv")
analyzer.load_data() \
        .filter_bots(exclude_bots=True) \
        .filter_by_status('online') \
        .filter_non_null('Act_Name')

print(f"Filtered records: {len(analyzer.df):,}")
analyzer.print_value_counts('Act_Name', top_n=10)
analyzer.save_data('online_user_activities.csv')
```

### Guild Analysis
```python
analyzer = DiscordPresenceAnalyzer("output_test.csv")
analyzer.load_data()

guild_stats = analyzer.get_guild_analysis()
print(f"Total guilds: {guild_stats['total_guilds']}")
print(f"Unique users: {guild_stats['unique_users']}")
```

### Platform Distribution
```python
analyzer = DiscordPresenceAnalyzer("output_test.csv")
analyzer.load_data().filter_bots(exclude_bots=True)

platform_dist = analyzer.get_value_counts('Platform', normalize=True)
print("Platform distribution:")
for platform, percentage in platform_dist.items():
    print(f"  {platform}: {percentage:.1%}")
```

## File Structure

- `output_analysis.py` - Main refactored analysis module
- `example_custom_analysis.py` - Example of extending the analyzer
- `test_outputs/` - Directory for analysis outputs

## Dependencies

- pandas
- pathlib (built-in)
- logging (built-in)
- typing (built-in)

## Migration from Original Code

The refactored version maintains compatibility while providing a much cleaner interface:

**Old way:**
```python
df = await get_df()
df = await filter_df(df, 'Is_Bot', False)
df = await filter_df(df, 'Main_Status', 'online')
tally = await get_tally(df, 'Act_Name')
await save_tally(tally, 'output.csv')
```

**New way:**
```python
analyzer = DiscordPresenceAnalyzer()
analyzer.load_data() \
        .filter_by_column('Is_Bot', False) \
        .filter_by_status('online')
analyzer.save_value_counts('Act_Name', 'output.csv')
```
