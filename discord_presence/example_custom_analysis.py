"""
Example script showing how to extend the DiscordPresenceAnalyzer with custom analysis functions.

This demonstrates the extensibility of the refactored output_analysis.py module.
"""

from output_analysis import DiscordPresenceAnalyzer
import pandas as pd
from typing import Dict, Any
import matplotlib.pyplot as plt
from datetime import datetime


class ExtendedDiscordAnalyzer(DiscordPresenceAnalyzer):
    """
    Extended analyzer with additional custom analysis methods.
    
    This shows how easy it is to add new functionality to the base analyzer.
    """
    
    def analyze_time_patterns(self) -> Dict[str, Any]:
        """
        Analyze temporal patterns in the data.
        
        Returns:
            Dictionary containing time-based analysis
        """
        if self.df is None:
            raise ValueError("No data loaded. Call load_data() first.")
        
        # Convert Unix timestamp to datetime
        self.df['datetime'] = pd.to_datetime(self.df['Unix_Timestamp'], unit='s')
        self.df['hour'] = self.df['datetime'].dt.hour
        self.df['day_of_week'] = self.df['datetime'].dt.day_name()
        
        analysis = {
            'hourly_activity': self.df['hour'].value_counts().sort_index().to_dict(),
            'daily_activity': self.df['day_of_week'].value_counts().to_dict(),
            'peak_hour': self.df['hour'].value_counts().idxmax(),
            'peak_day': self.df['day_of_week'].value_counts().idxmax()
        }
        
        return analysis
    
    def analyze_platform_preferences(self) -> Dict[str, Any]:
        """
        Analyze platform usage patterns.
        
        Returns:
            Dictionary containing platform analysis
        """
        if self.df is None:
            raise ValueError("No data loaded. Call load_data() first.")
        
        platform_analysis = {}
        
        # Overall platform distribution
        platform_analysis['platform_distribution'] = self.get_value_counts('Platform').to_dict()
        
        # Platform by status
        if 'Main_Status' in self.df.columns:
            platform_by_status = self.df.groupby('Main_Status')['Platform'].value_counts()
            platform_analysis['platform_by_status'] = platform_by_status.to_dict()
        
        # Platform by activity type
        if 'Act_Type' in self.df.columns:
            platform_by_activity = self.df.groupby('Act_Type')['Platform'].value_counts()
            platform_analysis['platform_by_activity'] = platform_by_activity.to_dict()
        
        return platform_analysis
    
    def analyze_user_behavior(self) -> Dict[str, Any]:
        """
        Analyze individual user behavior patterns.
        
        Returns:
            Dictionary containing user behavior analysis
        """
        if self.df is None:
            raise ValueError("No data loaded. Call load_data() first.")
        
        user_analysis = {}
        
        # Users with most activity records
        user_activity_counts = self.df['User_ID'].value_counts()
        user_analysis['most_active_users'] = user_activity_counts.head(10).to_dict()
        
        # Average activities per user
        user_analysis['avg_activities_per_user'] = user_activity_counts.mean()
        
        # Users by status distribution
        user_status_dist = self.df.groupby('User_ID')['Main_Status'].apply(
            lambda x: x.value_counts().to_dict()
        )
        
        # Find users who are always online vs mixed status
        always_online_users = user_status_dist[
            user_status_dist.apply(lambda x: len(x) == 1 and 'online' in x)
        ]
        user_analysis['always_online_users_count'] = len(always_online_users)
        
        return user_analysis
    
    def generate_activity_report(self, output_file: str = "activity_report.txt") -> None:
        """
        Generate a comprehensive text report of the analysis.
        
        Args:
            output_file: Path to save the report
        """
        if self.df is None:
            raise ValueError("No data loaded. Call load_data() first.")
        
        with open(output_file, 'w') as f:
            f.write("Discord Presence Analysis Report\n")
            f.write("=" * 50 + "\n\n")
            
            # Basic info
            info = self.get_data_info()
            f.write(f"Dataset Overview:\n")
            f.write(f"- Total records: {info['total_records']:,}\n")
            f.write(f"- Date range: {self.df['Unix_Timestamp'].min():.0f} to {self.df['Unix_Timestamp'].max():.0f}\n")
            f.write(f"- Unique users: {self.df['User_ID'].nunique():,}\n")
            f.write(f"- Unique guilds: {self.df['Guild_Name'].nunique()}\n\n")
            
            # Activity summary
            activity_summary = self.get_activity_summary()
            f.write("Activity Summary:\n")
            f.write(f"- Top 5 activities:\n")
            for activity, count in list(activity_summary.get('top_activities', {}).items())[:5]:
                f.write(f"  * {activity}: {count:,}\n")
            f.write("\n")
            
            # Platform analysis
            platform_analysis = self.analyze_platform_preferences()
            f.write("Platform Distribution:\n")
            for platform, count in platform_analysis['platform_distribution'].items():
                f.write(f"- {platform}: {count:,}\n")
            f.write("\n")
            
            # Time patterns
            time_analysis = self.analyze_time_patterns()
            f.write(f"Peak Activity:\n")
            f.write(f"- Peak hour: {time_analysis['peak_hour']}:00\n")
            f.write(f"- Peak day: {time_analysis['peak_day']}\n")
            
        print(f"Report saved to {output_file}")


def example_custom_analysis():
    """
    Example function showing how to use the extended analyzer.
    """
    # Initialize the extended analyzer
    analyzer = ExtendedDiscordAnalyzer("output_test.csv")
    analyzer.load_data()
    
    # Apply some filters
    analyzer.filter_bots(exclude_bots=True)
    analyzer.filter_by_status('online')
    
    print("=== Custom Time Pattern Analysis ===")
    time_patterns = analyzer.analyze_time_patterns()
    print(f"Peak activity hour: {time_patterns['peak_hour']}:00")
    print(f"Peak activity day: {time_patterns['peak_day']}")
    
    print("\n=== Platform Preferences Analysis ===")
    platform_prefs = analyzer.analyze_platform_preferences()
    print("Platform distribution:")
    for platform, count in platform_prefs['platform_distribution'].items():
        print(f"  {platform}: {count:,}")
    
    print("\n=== User Behavior Analysis ===")
    user_behavior = analyzer.analyze_user_behavior()
    print(f"Average activities per user: {user_behavior['avg_activities_per_user']:.1f}")
    print(f"Users always online: {user_behavior['always_online_users_count']}")
    
    # Generate comprehensive report
    print("\n=== Generating Report ===")
    analyzer.generate_activity_report("test_outputs/comprehensive_report.txt")


if __name__ == "__main__":
    example_custom_analysis()
