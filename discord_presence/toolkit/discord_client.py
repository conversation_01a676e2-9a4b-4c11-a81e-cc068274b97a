"""
Discord Presence Client

This module contains the main client class for scraping Discord presence data.
It orchestrates the HTTP and Gateway clients to collect member presence information
from Discord guilds.

The client handles:
- Authentication and connection management
- Guild subscription and member list scraping
- Real-time presence updates
- Data processing and CSV output
- Progress tracking and logging
"""

import asyncio
import json
import logging
import time
from typing import Dict, Optional, List, Any, Set

from toolkit.config import ToolkitConfig, CSV_HEADERS
from toolkit.gateway_client import DiscordGatewayClient, GatewayEvent
from toolkit.http_client import DiscordHTTPClient
from toolkit.models import DiscordMember, GuildInfo, ScrapingProgress
from toolkit.utils import CSVWriter, JSONLogger, filter_accessible_channels, ProgressTracker


logger = logging.getLogger(__name__)


class DiscordPresenceClient:
    """
    Main client for Discord presence scraping.

    This client coordinates HTTP and Gateway connections to scrape
    member presence data from Discord guilds.

    Attributes:
        config: Configuration object
        http: HTTP client for API requests
        gateway: Gateway client for WebSocket connection
        guilds: Dictionary of guild information
        members: Dictionary of member data by guild
        progress: Dictionary of scraping progress by guild
    """

    def __init__(
        self,
        token: Optional[str] = None,
        log_path: Optional[str] = None,
        output_path: Optional[str] = None,
        per_guild_limit: int = 50000,
        scraped_channels: int = 1,
        config: Optional[ToolkitConfig] = None
    ):
        """
        Initialize the Discord presence client.

        Args:
            token: Discord authentication token (overrides config)
            log_path: Path for raw logs (overrides config)
            output_path: Path for CSV output (overrides config)
            per_guild_limit: Maximum members per guild (overrides config)
            scraped_channels: Number of channels to scrape (overrides config)
            config: Configuration object (if not provided, loads from environment)
        """
        # Load configuration
        self.config = config or ToolkitConfig.from_environment()

        # Override config with explicit parameters
        if token:
            self.config.discord.token = token
        if log_path:
            self.config.output.log_path = log_path
        if output_path:
            self.config.output.output_path = output_path
        if per_guild_limit != 50000:  # Only override if explicitly set
            self.config.scraping.per_guild_limit = per_guild_limit
        if scraped_channels != 1:  # Only override if explicitly set
            self.config.scraping.scraped_channels = scraped_channels

        # Validate configuration
        self.config.validate()

        # Initialize clients
        self.http = DiscordHTTPClient(
            self.config.discord.token,
            client_type=self.config.discord.client_type,
            timeout=self.config.connection.connection_timeout
        )
        self.gateway = DiscordGatewayClient(
            self.config.discord.token,
            enable_compression=self.config.connection.enable_compression,
            debug_logging=self.config.connection.debug_logging
        )

        # Core state
        self.user_id: Optional[str] = None
        self.session_id: Optional[str] = None

        # Data storage
        self.guilds: Dict[str, GuildInfo] = {}
        self.members: Dict[str, Dict[str, DiscordMember]] = {}
        self.channels: Dict[str, List[str]] = {}
        self.sidebar_rank: Dict[str, List[Optional[str]]] = {}
        self.used_ranges: Dict[str, List[int]] = {}
        self.progress: Dict[str, ScrapingProgress] = {}

        # Scraping state
        self.guild_index = 0
        self.current_guild = ''
        self.expected_responses: List[str] = []
        self.sidebar_index = 0
        self.new_guild = False
        self.scraped_members: Set[str] = set()  # Track scraped member IDs

        # Output handlers
        self.csv_writer: Optional[CSVWriter] = None
        self.json_logger: Optional[JSONLogger] = None
        self.progress_tracker: Optional[ProgressTracker] = None

        # Connection state
        self.ready = False
        self.maintain_connection = False
        self._ready_event = asyncio.Event()

        # Cache for deduplication
        self.response_cache: List[str] = []
        self.cache_size = 10

        logger.info("Discord Presence Client initialized")
        logger.debug(f"Configuration: {self.config.to_dict()}")

        # Setup event handlers
        self._setup_handlers()
    
    def _setup_handlers(self) -> None:
        """
        Setup WebSocket event handlers.

        Registers event handlers for various Discord Gateway events
        to process incoming data and manage the scraping process.
        """

        @self.gateway.on(GatewayEvent.RAW)
        async def write_log(data: Dict[str, Any]) -> None:
            """Write raw logging data to JSON log."""
            if self.json_logger:
                self.json_logger.log(data)

        @self.gateway.on(GatewayEvent.READY)
        async def on_ready(data: Dict[str, Any]) -> None:
            """
            Handle READY event - initial connection established.

            This event contains initial user and guild information.
            """
            self.user_id = data["user"]["id"]
            self.session_id = data.get("session_id")

            username = data['user']['username']
            discriminator = data['user'].get('discriminator', '0000')
            logger.info(f"Connected as {username}#{discriminator} (ID: {self.user_id})")

            # Update reconnection URL
            self.gateway.reconnect_url = data.get('resume_gateway_url', 'wss://gateway.discord.gg/')

            # Process initial guild data
            guild_count = len(data.get('guilds', []))
            logger.info(f"Processing {guild_count} guilds from READY event")

            for guild_data in data.get('guilds', []):
                await self._process_initial_guild(guild_data)

            # Start scraping first guild
            if self.guilds:
                guild_id = list(self.guilds.keys())[self.guild_index]
                self.current_guild = guild_id
                self.expected_responses = ['SYNC 0', 'SYNC 100', 'SYNC 200']

                logger.info(f"Starting scraping with guild: {guild_id}")
                await self._subscribe_to_guild(guild_id)
            else:
                logger.warning("No guilds available for scraping")


        @self.gateway.on(GatewayEvent.READY_SUPPLEMENTAL)
        async def on_ready_supplemental(data: Dict[str, Any]) -> None:
            """
            Handle READY_SUPPLEMENTAL - contains initial member/presence data.

            This event provides additional guild data including initial
            member and presence information.
            """
            guilds = data.get("guilds", [])
            merged_members = data.get("merged_members", [])
            merged_presences = data.get("merged_presences", {})

            logger.info(f"Processing READY_SUPPLEMENTAL for {len(guilds)} guilds")

            # Process each guild's supplemental data
            for i, guild_data in enumerate(guilds):
                guild_id = guild_data["id"]

                # Ensure guild exists in our data
                if guild_id not in self.guilds:
                    logger.warning(f"Received supplemental data for unknown guild: {guild_id}")
                    continue

                # Initialize storage if needed
                self._ensure_guild_storage(guild_id)

                # Process merged presences for this guild
                guild_presences = merged_presences.get("guilds", [])
                if i < len(guild_presences):
                    presence_count = 0
                    for presence_data in guild_presences[i]:
                        if not presence_data:
                            continue

                        user_id = presence_data.get("user_id")
                        if user_id:
                            # Create member from presence data
                            member = DiscordMember.from_discord_data(
                                user_id, guild_id, {'presence': presence_data}
                            )
                            self.members[guild_id][user_id] = member
                            presence_count += 1

                    logger.debug(f"Processed {presence_count} presences for guild {guild_id}")

            # Start sidebar scraping
            if self.current_guild:
                self.expected_responses = ['SYNC 0', 'SYNC 100', 'SYNC 200']
                await self._scrape_guild_sidebar(self.current_guild)

            # Mark as ready
            self.ready = True
            self._ready_event.set()
            logger.info("Client is ready for scraping")
            

        @self.gateway.on(GatewayEvent.GUILD_CREATE)
        async def on_guild_create(data: Dict[str, Any]) -> None:
            """
            Handle GUILD_CREATE - full guild data with members/presences.

            This event is received when a guild becomes available or when
            the client joins a new guild.
            """
            guild_id = data["id"]
            logger.debug(f"Processing GUILD_CREATE for guild {guild_id}")

            # Update guild info if we already have it, otherwise create new
            if guild_id in self.guilds:
                # Update existing guild info
                self.guilds[guild_id].properties = data
            else:
                # Create new guild info
                guild_info = GuildInfo.from_discord_data(data)
                self.guilds[guild_id] = guild_info
                logger.info(f"Added new guild: {guild_info.name} ({guild_id})")

            # Ensure storage is initialized
            self._ensure_guild_storage(guild_id)

            # Process members from GUILD_CREATE (usually just the client user)
            member_count = 0
            for member_data in data.get("members", []):
                user_id = member_data['user']['id']
                member = DiscordMember.from_discord_data(user_id, guild_id, member_data)
                self.members[guild_id][user_id] = member
                member_count += 1

            if member_count > 0:
                logger.debug(f"Processed {member_count} members from GUILD_CREATE")

            # Process presences from GUILD_CREATE (rarely present)
            presence_count = 0
            for presence_data in data.get("presences", []):
                user_data = presence_data.get("user", {})
                user_id = user_data.get("id") if user_data else presence_data.get("user_id")

                if user_id:
                    try:
                        # Create or update member with presence data
                        if user_id in self.members[guild_id]:
                            self.members[guild_id][user_id].presence_data = presence_data
                        else:
                            member = DiscordMember.from_discord_data(
                                user_id, guild_id, {'presence': presence_data}
                            )
                            self.members[guild_id][user_id] = member
                        presence_count += 1

                    except Exception as e:
                        logger.error(f"Error processing presence in GUILD_CREATE: {e}")

            if presence_count > 0:
                logger.debug(f"Processed {presence_count} presences from GUILD_CREATE")

        @self.gateway.on(GatewayEvent.PRESENCE_UPDATE)
        async def on_presence_update(data: Dict[str, Any]) -> None:
            """
            Handle PRESENCE_UPDATE - real-time presence changes.

            Updates member presence data when users change their status
            or activities.
            """
            user_data = data.get("user", {})
            user_id = user_data.get("id")
            guild_id = data.get("guild_id")

            if not user_id or not guild_id:
                logger.warning("Received presence update without user_id or guild_id")
                return

            # Update member presence data
            if guild_id in self.members and user_id in self.members[guild_id]:
                self.members[guild_id][user_id].presence_data = data
                logger.debug(f"Updated presence for user {user_id} in guild {guild_id}")
            else:
                # Create new member from presence data
                member = DiscordMember.from_discord_data(
                    user_id, guild_id, {'presence': data}
                )
                if guild_id not in self.members:
                    self.members[guild_id] = {}
                self.members[guild_id][user_id] = member
                logger.debug(f"Created new member from presence update: {user_id}")

        @self.gateway.on(GatewayEvent.GUILD_MEMBER_LIST_UPDATE)
        async def on_member_update(data: Dict[str, Any]) -> None:
            """
            Handle GUILD_MEMBER_LIST_UPDATE - member list changes.

            This is the primary event for receiving member data during
            sidebar scraping operations.
            """
            timestamp = time.time()
            guild_id = data.get('guild_id')

            if not guild_id:
                logger.warning("Received member list update without guild_id")
                return

            guild_name = self.guilds.get(guild_id, GuildInfo('', 'Unknown')).name

            # Deduplicate responses using cache
            datastring = str(data.get('ops', []))
            if datastring in self.response_cache:
                return

            self.response_cache.append(datastring)
            if len(self.response_cache) > self.cache_size:
                self.response_cache.pop(0)

            # Process operations
            ops_processed = 0
            for op in data.get('ops', []):
                op_type = op.get('op')

                if op_type == 'SYNC':
                    await self._process_sync_operation(op, guild_id)
                    ops_processed += 1

                elif op_type == 'UPDATE':
                    await self._process_update_operation(op, guild_id)
                    ops_processed += 1

                elif op_type == 'DELETE':
                    await self._process_delete_operation(op, guild_id)
                    ops_processed += 1

                elif op_type == 'INSERT':
                    await self._process_insert_operation(op, guild_id)
                    ops_processed += 1

                elif op_type == 'INVALIDATE':
                    await self._process_sync_operation(op, guild_id)
                    ops_processed += 1

                else:
                    logger.debug(f"Unknown operation type: {op_type}")

            if ops_processed > 0:
                logger.debug(f"Processed {ops_processed} operations for guild {guild_name}")

        @self.gateway.on(GatewayEvent.RECONNECT)
        async def on_reconnect(data: Any) -> None:
            """Handle reconnection events."""
            logger.info('Reconnection requested by gateway')
            await self.reconnect()

        @self.gateway.on(GatewayEvent.SUBSCRIBE)
        async def restart_subscription(data: Any) -> None:
            """Handle subscription restart events."""
            if self.current_guild:
                logger.info(f'Restarting subscription for guild {self.current_guild}')
                await self._subscribe_to_guild(self.current_guild)
                await self._scrape_guild_sidebar(self.current_guild)
            else:
                logger.warning('No current guild to restart subscription for')

    # Helper methods for event processing

    async def _process_initial_guild(self, guild_data: Dict[str, Any]) -> None:
        """
        Process initial guild data from READY event.

        Args:
            guild_data: Raw guild data from Discord
        """
        guild_info = GuildInfo.from_discord_data(guild_data)
        self.guilds[guild_info.guild_id] = guild_info

        # Initialize storage
        self._ensure_guild_storage(guild_info.guild_id)

        # Filter accessible channels
        channels = filter_accessible_channels(guild_data)
        self.channels[guild_info.guild_id] = channels

        # Initialize ranges
        self.used_ranges[guild_info.guild_id] = [0, 1, 2]

        logger.debug(f"Initialized guild {guild_info.name} with {len(channels)} channels")

    def _ensure_guild_storage(self, guild_id: str) -> None:
        """
        Ensure storage structures exist for a guild.

        Args:
            guild_id: Guild ID to initialize storage for
        """
        if guild_id not in self.members:
            self.members[guild_id] = {}
        if guild_id not in self.sidebar_rank:
            self.sidebar_rank[guild_id] = [None] * 300
        if guild_id not in self.progress:
            self.progress[guild_id] = ScrapingProgress(guild_id)

    async def _process_sync_operation(self, op: Dict[str, Any], guild_id: str) -> None:
        """
        Process SYNC operation from member list update.

        Args:
            op: Operation data
            guild_id: Guild ID
        """
        await self.process_response(op, guild_id)

    async def _process_update_operation(self, op: Dict[str, Any], guild_id: str) -> None:
        """
        Process UPDATE operation from member list update.

        Args:
            op: Operation data
            guild_id: Guild ID
        """
        items = op.get('items', [op.get('item', {})])
        for item in items:
            member_data = item.get('member')
            if member_data:
                user_id = member_data['user']['id']
                member = DiscordMember.from_discord_data(user_id, guild_id, member_data)
                self.members[guild_id][user_id] = member

    async def _process_delete_operation(self, op: Dict[str, Any], guild_id: str) -> None:
        """
        Process DELETE operation from member list update.

        Args:
            op: Operation data
            guild_id: Guild ID
        """
        index = op.get('index')
        if index is not None and guild_id in self.sidebar_rank:
            if 0 <= index < len(self.sidebar_rank[guild_id]):
                self.sidebar_rank[guild_id].pop(index)

    async def _process_insert_operation(self, op: Dict[str, Any], guild_id: str) -> None:
        """
        Process INSERT operation from member list update.

        Args:
            op: Operation data
            guild_id: Guild ID
        """
        index = op.get('index')
        item = op.get('item', {})
        member_data = item.get('member')

        if index is not None and guild_id in self.sidebar_rank:
            if member_data is None:
                self.sidebar_rank[guild_id].insert(index, None)
            else:
                user_id = member_data['user']['id']
                member = DiscordMember.from_discord_data(user_id, guild_id, member_data)
                self.members[guild_id][user_id] = member
                self.sidebar_rank[guild_id].insert(index, user_id)

    async def process_response(self, op: Dict[str, Any], guild_id: str) -> None:
        """
        Process response from SYNC or INVALIDATE operations.

        This method manages the scraping flow by tracking expected responses
        and advancing to new ranges or guilds when appropriate.

        Args:
            op: Operation data
            guild_id: Guild ID
        """
        op_type = op.get('op', 'UNKNOWN')
        op_range = op.get('range', [0])
        exp_code = f"{op_type} {op_range[0]}"

        logger.debug(f"Processing response: {exp_code}")
        logger.debug(f"Expected responses: {self.expected_responses}")
        logger.debug(f"New guild flag: {self.new_guild}")

        # Remove from expected responses
        if exp_code in self.expected_responses:
            self.expected_responses.remove(exp_code)

        # Wait for all expected responses before proceeding
        if len(self.expected_responses) != 0 or guild_id != self.current_guild:
            return

        guild_list = list(self.guilds.keys())

        if not self.new_guild:
            # Request new ranges for current guild
            logger.info("Requesting new member ranges")
            i = self.sidebar_index
            exp_inval = [f"INVALIDATE {j*100}" for j in [i+1, i+2]]
            self.expected_responses.extend(exp_inval)
            self.sidebar_index += 2

            # Update progress
            if guild_id in self.progress:
                self.progress[guild_id].update_progress(ranges_added=2)

        elif self.guild_index < len(guild_list) - 1:
            # Move to next guild
            logger.info("Moving to next guild")
            await self._output_current_guild_data()

            self.sidebar_index = 0
            self.guild_index += 1
            self.current_guild = guild_list[self.guild_index]
            self.new_guild = False
            self.expected_responses.append('SYNC 0')

            guild_name = self.guilds[self.current_guild].name
            logger.info(f"Starting scraping for guild: {guild_name} ({self.current_guild})")
            await self._subscribe_to_guild(self.current_guild)

        else:
            # All guilds completed
            logger.info("All guilds completed, finishing scraping")
            await self._output_current_guild_data()
            await self.close()
            return

        # Update ranges and continue scraping
        i = self.sidebar_index
        self.used_ranges[self.current_guild] = [0, i+1, i+2]

        # Extend sidebar rank if needed
        current_length = len(self.sidebar_rank[self.current_guild])
        needed_length = (i+3) * 100
        if current_length < needed_length:
            extension = [None] * (needed_length - current_length)
            self.sidebar_rank[self.current_guild].extend(extension)

        exp_sync = [f"SYNC {j*100}" for j in [i+1, i+2]]
        self.expected_responses.extend(exp_sync)
        logger.debug(f"Updated expected responses: {self.expected_responses}")

        await self._scrape_guild_sidebar(self.current_guild)

        # Check if we've reached the end of this guild
        items = op.get('items', [])
        if len(items) == 0 and guild_id == self.current_guild:
            self.new_guild = True
            logger.debug("Reached end of current guild")

    async def _output_current_guild_data(self) -> None:
        """
        Output current guild's member data to CSV.

        Processes all collected member data for the current guild
        and writes it to the CSV output file.
        """
        if not self.current_guild or self.current_guild not in self.members:
            logger.warning("No current guild or member data to output")
            return

        if not self.csv_writer:
            logger.warning("CSV writer not initialized")
            return

        timestamp = time.time()
        guild_id = self.current_guild
        guild_name = self.guilds[guild_id].name

        total_members = 0
        total_rows = 0

        # Process all members in the guild
        for user_id, member in self.members[guild_id].items():
            if user_id in self.scraped_members:
                continue  # Skip already processed members

            # Convert member to CSV rows
            rows = member.to_csv_rows(timestamp)
            self.csv_writer.add_rows(rows)

            total_members += 1
            total_rows += len(rows)
            self.scraped_members.add(user_id)

        # Update progress
        if guild_id in self.progress:
            self.progress[guild_id].update_progress(members_added=total_members)

        logger.info(f"Output {total_members} members ({total_rows} rows) for guild {guild_name}")

        # Flush CSV data
        self.csv_writer.flush()
    
    async def _initialize_output_handlers(self) -> None:
        """
        Initialize output handlers for CSV and JSON logging.
        """
        # Initialize CSV writer
        if self.config.output.enable_csv_output:
            self.csv_writer = CSVWriter(
                self.config.output.output_path,
                CSV_HEADERS,
                self.config.output.csv_buffer_size
            )
            logger.info(f"Initialized CSV writer: {self.config.output.output_path}")

        # Initialize JSON logger
        self.json_logger = JSONLogger(self.config.output.log_path)
        logger.info(f"Initialized JSON logger: {self.config.output.log_path}")

        # Initialize progress tracker
        if self.config.output.enable_progress_logging:
            total_guilds = len(self.guilds) if self.guilds else None
            self.progress_tracker = ProgressTracker(total_guilds, "Guild scraping")

    async def _cleanup_output_handlers(self) -> None:
        """
        Clean up output handlers and flush remaining data.
        """
        if self.csv_writer:
            self.csv_writer.close()
            logger.info("CSV writer closed")

        if self.json_logger:
            self.json_logger.close()
            logger.info("JSON logger closed")

        if self.progress_tracker:
            self.progress_tracker.finish()

    async def _subscribe_to_guild(self, guild_id: str) -> None:
        """
        Subscribe to a guild for presence updates.

        Args:
            guild_id: Guild ID to subscribe to
        """
        if guild_id not in self.channels:
            logger.warning(f"No channels available for guild {guild_id}")
            return

        guild_name = self.guilds.get(guild_id, GuildInfo('', 'Unknown')).name
        logger.info(f"Subscribing to guild: {guild_name} ({guild_id})")

        # Get limited number of channels to subscribe to
        channels = self.channels[guild_id][:self.config.scraping.scraped_channels]

        try:
            # Request channel statuses (mimics web client behavior)
            await self.gateway.request_channel_statuses(guild_id)
            await asyncio.sleep(self.config.scraping.request_delay)

            # Subscribe to guild with specified settings
            await self.gateway.subscribe_to_guild(
                guild_id,
                typing=self.config.scraping.enable_typing_events,
                activities=self.config.scraping.enable_presence_updates,
                threads=self.config.scraping.enable_thread_events,
                channels=channels,
                ranges=[0]  # Start with first range
            )

            await asyncio.sleep(self.config.scraping.request_delay)
            await self.gateway.request_channel_statuses(guild_id)

            logger.debug(f"Successfully subscribed to guild {guild_id} with {len(channels)} channels")

        except Exception as e:
            logger.error(f"Failed to subscribe to guild {guild_id}: {e}")
            raise

    async def _scrape_guild_sidebar(self, guild_id: str) -> None:
        """
        Scrape guild sidebar (member list) for presence data.

        Args:
            guild_id: Guild ID to scrape
        """
        if guild_id not in self.used_ranges or guild_id not in self.channels:
            logger.warning(f"Missing data for guild {guild_id}, cannot scrape sidebar")
            return

        ranges = self.used_ranges[guild_id]
        channels = self.channels[guild_id][:self.config.scraping.scraped_channels]

        guild_name = self.guilds.get(guild_id, GuildInfo('', 'Unknown')).name
        logger.debug(f"Scraping sidebar for {guild_name}: ranges {ranges}, channels {len(channels)}")

        try:
            await self.gateway.subscribe_to_guild(
                guild_id,
                channels=channels,
                ranges=ranges,
                activities=self.config.scraping.enable_presence_updates
            )

            # Update progress
            if guild_id in self.progress:
                self.progress[guild_id].update_progress(channels_added=len(channels))

        except Exception as e:
            logger.error(f"Failed to scrape sidebar for guild {guild_id}: {e}")
            raise

    async def reconnect(self) -> None:
        """
        Reconnect to Discord Gateway.
        """
        logger.info("Reconnecting to Discord Gateway")
        await self.gateway.reconnect()

    async def connect(self) -> None:
        """
        Connect to Discord and wait for ready state.

        Raises:
            ValueError: If authentication fails
            ConnectionError: If connection fails
        """
        logger.info("Connecting to Discord...")

        # Verify token and get user info
        try:
            user_data = await self.http.get_me()
            self.user_id = user_data["id"]
            username = user_data['username']
            discriminator = user_data.get('discriminator', '0000')
            logger.info(f"Authenticated as {username}#{discriminator} (ID: {self.user_id})")
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            raise ValueError(f"Authentication failed: {e}")

        # Get gateway URL
        try:
            gateway_data = await self.http.get_gateway()
            gateway_url = gateway_data["url"]
            logger.debug(f"Gateway URL: {gateway_url}")
        except Exception as e:
            logger.error(f"Failed to get gateway URL: {e}")
            raise ConnectionError(f"Failed to get gateway URL: {e}")

        # Initialize output handlers
        await self._initialize_output_handlers()

        # Set connection flag
        self.maintain_connection = True

        # Connect to gateway
        try:
            await self.gateway.connect(gateway_url)
        except Exception as e:
            logger.error(f"Gateway connection failed: {e}")
            raise ConnectionError(f"Gateway connection failed: {e}")

        # Wait for ready state
        logger.info("Waiting for ready state...")
        await self._ready_event.wait()

        logger.info("Client ready for scraping!")

    async def close(self) -> None:
        """
        Close connections and cleanup resources.
        """
        logger.info("Closing connections and cleaning up...")

        # Set connection flag
        self.maintain_connection = False

        # Output any remaining data
        if self.current_guild:
            await self._output_current_guild_data()

        # Close connections
        try:
            await self.gateway.close()
            logger.debug("Gateway connection closed")
        except Exception as e:
            logger.warning(f"Error closing gateway: {e}")

        try:
            await self.http.close()
            logger.debug("HTTP client closed")
        except Exception as e:
            logger.warning(f"Error closing HTTP client: {e}")

        # Clean up output handlers
        await self._cleanup_output_handlers()

        logger.info("Cleanup completed")

    async def run(self) -> None:
        """
        Main run method for the Discord presence client.

        Connects to Discord, starts scraping, and maintains the connection
        until all guilds are processed or an error occurs.
        """
        try:
            await self.connect()

            # Keep connection alive while scraping
            logger.info("Starting main scraping loop...")
            while self.maintain_connection:
                await asyncio.sleep(1)

                # Check if we should stop due to limits
                if self._should_stop_scraping():
                    logger.info("Scraping limits reached, stopping...")
                    break

        except KeyboardInterrupt:
            logger.info("Shutdown requested by user")
        except Exception as e:
            logger.error(f"Unexpected error during scraping: {e}")
            raise
        finally:
            await self.close()

    def _should_stop_scraping(self) -> bool:
        """
        Check if scraping should stop based on configured limits.

        Returns:
            True if scraping should stop, False otherwise
        """
        # Check per-guild member limit
        if self.current_guild and self.current_guild in self.members:
            member_count = len(self.members[self.current_guild])
            if member_count >= self.config.scraping.per_guild_limit:
                logger.info(f"Reached per-guild limit of {self.config.scraping.per_guild_limit} members")
                return True

        # Check total scraped members
        total_scraped = len(self.scraped_members)
        if (self.config.scraping.max_total_members > 0 and
            total_scraped >= self.config.scraping.max_total_members):
            logger.info(f"Reached total member limit of {self.config.scraping.max_total_members}")
            return True

        return False

    # Context manager support
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()