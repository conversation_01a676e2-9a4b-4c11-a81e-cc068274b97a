#!/usr/bin/env python3
"""
Discord Presence Toolkit - Main Entry Point

This module provides the main entry point for the Discord presence scraping toolkit.
It handles configuration loading, argument parsing, and client initialization.

Usage:
    python main.py [--token TOKEN] [--log-path PATH] [--config CONFIG]

Environment Variables:
    DISCORD_TOKEN: Discord bot/user token for authentication
    LOG_PATH: Path for raw log output (default: ./raw_logs.txt)
    OUTPUT_PATH: Path for CSV output (default: ./output.csv)
"""

import argparse
import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import Optional

from dotenv import load_dotenv

from toolkit.discord_client import DiscordPresenceClient
from toolkit.config import ToolkitConfig, DiscordConfig, OutputConfig, ScrapingConfig, ConnectionConfig

def setup_logging(log_level: str = "INFO") -> None:
    """
    Configure logging for the application.

    Args:
        log_level: Logging level (DEBUG, INF<PERSON>, WARNING, ERROR, CRITICAL)
    """
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('discord_toolkit.log')
        ]
    )


def parse_arguments() -> argparse.Namespace:
    """
    Parse command line arguments.

    Returns:
        Parsed arguments namespace
    """
    parser = argparse.ArgumentParser(
        description="Discord Presence Scraping Toolkit",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )

    parser.add_argument(
        '--token', '-t',
        type=str,
        help='Discord token (overrides DISCORD_TOKEN env var)'
    )

    parser.add_argument(
        '--log-path', '-l',
        type=str,
        default='./raw_logs.txt',
        help='Path for raw log output (default: ./raw_logs.txt)'
    )

    parser.add_argument(
        '--output-path', '-o',
        type=str,
        default='./output.csv',
        help='Path for CSV output (default: ./output.csv)'
    )

    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO',
        help='Set the logging level (default: INFO)'
    )

    parser.add_argument(
        '--config', '-c',
        type=str,
        help='Path to configuration file'
    )

    return parser.parse_args()


def load_configuration(config_path: Optional[str] = None) -> dict:
    """
    Load configuration from environment variables and optional config file.

    Args:
        config_path: Optional path to configuration file

    Returns:
        Configuration dictionary
    """
    # Load environment variables
    load_dotenv()

    config = {
        'token': os.getenv('DISCORD_TOKEN'),
        'log_path': os.getenv('LOG_PATH', './raw_logs.txt'),
        'output_path': os.getenv('OUTPUT_PATH', './output.csv'),
        'per_guild_limit': int(os.getenv('PER_GUILD_LIMIT', '50000')),
        'scraped_channels': int(os.getenv('SCRAPED_CHANNELS', '1')),
    }

    # TODO: Add config file loading if needed
    if config_path:
        logging.info(f"Config file loading not yet implemented: {config_path}")

    return config


def validate_configuration(config: dict, args: argparse.Namespace) -> dict:
    """
    Validate and merge configuration from multiple sources.

    Args:
        config: Base configuration from environment/file
        args: Command line arguments

    Returns:
        Final validated configuration

    Raises:
        ValueError: If required configuration is missing or invalid
    """
    # Override with command line arguments
    if args.token:
        config['token'] = args.token
    if args.log_path:
        config['log_path'] = args.log_path
    if args.output_path:
        config['output_path'] = args.output_path

    # Validate required fields
    if not config['token']:
        raise ValueError(
            "Discord token is required. Set DISCORD_TOKEN environment variable "
            "or use --token argument."
        )

    # Ensure output directories exist
    for path_key in ['log_path', 'output_path']:
        path = Path(config[path_key])
        path.parent.mkdir(parents=True, exist_ok=True)

    return config


async def main() -> int:
    """
    Main entry point for the Discord presence toolkit.

    Returns:
        Exit code (0 for success, non-zero for error)
    """
    try:
        # Parse arguments and setup logging
        args = parse_arguments()
        setup_logging(args.log_level)

        logger = logging.getLogger(__name__)
        logger.info("Starting Discord Presence Toolkit")

        # Load and validate configuration
        config = load_configuration(args.config)
        config = validate_configuration(config, args)

        logger.info(f"Using log path: {config['log_path']}")
        logger.info(f"Using output path: {config['output_path']}")

        # Create configuration object
        toolkit_config = ToolkitConfig(
            discord=DiscordConfig(token=config['token']),
            output=OutputConfig(
                log_path=config['log_path'],
                output_path=config['output_path']
            ),
            scraping=ScrapingConfig(
                per_guild_limit=config['per_guild_limit'],
                scraped_channels=config['scraped_channels']
            ),
            connection=ConnectionConfig()
        )

        # Initialize and run client
        client = DiscordPresenceClient(config=toolkit_config)
        await client.run()

        logger.info("Discord Presence Toolkit completed successfully")
        return 0

    except KeyboardInterrupt:
        logging.info("User requested cancellation")
        return 130  # Standard exit code for SIGINT

    except ValueError as e:
        logging.error(f"Configuration error: {e}")
        return 1

    except Exception as e:
        logging.error(f"Unexpected error: {e}", exc_info=True)
        return 1


if __name__ == '__main__':
    sys.exit(asyncio.run(main()))