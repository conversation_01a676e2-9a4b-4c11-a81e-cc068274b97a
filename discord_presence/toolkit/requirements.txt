# Discord Presence Toolkit Dependencies
# Core dependencies for the Discord presence scraping toolkit

# HTTP client for Discord API
httpx>=0.28.0,<1.0.0

# WebSocket client for Discord Gateway
websockets>=15.0.0,<16.0.0

# Environment variable management
python-dotenv>=1.0.0,<2.0.0

# Type hints support for older Python versions
typing-extensions>=4.0.0,<5.0.0

# Development dependencies (optional)
# Uncomment for development/testing
# pytest>=7.0.0,<8.0.0
# pytest-asyncio>=0.21.0,<1.0.0
# black>=23.0.0,<24.0.0
# mypy>=1.0.0,<2.0.0

# Note: Other dependencies are automatically installed as sub-dependencies
# of the main packages above. This keeps the requirements minimal and
# reduces potential version conflicts.