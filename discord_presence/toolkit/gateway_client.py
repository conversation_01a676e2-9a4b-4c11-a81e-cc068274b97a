"""
Discord Gateway Client Module

This module provides a WebSocket client for connecting to Discord's Gateway API.
It handles real-time events, presence updates, and guild member information.

Features:
- Automatic reconnection with session resumption
- Zlib compression support for reduced bandwidth
- Comprehensive event handling system
- Rate limiting and heartbeat management
- Guild subscription management for presence data
- Robust error handling and logging

The gateway client is designed specifically for presence scraping and member
list monitoring, implementing Discord's undocumented member list subscription
protocols.
"""

import asyncio
import json
import logging
import time
import zlib
from enum import Enum
from typing import Dict, Any, Optional, Callable, List, Union

import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException


logger = logging.getLogger(__name__)


class GatewayOPCodes:
    """
    Discord Gateway OPCodes.

    These are the operation codes used in WebSocket communication
    with Discord's Gateway API.
    """
    DISPATCH = 0                    # Receive events
    HEARTBEAT = 1                   # Send/receive heartbeat
    IDENTIFY = 2                    # Start a new session
    PRESENCE = 3                    # Update presence
    VOICE_STATE = 4                 # Update voice state
    RESUME = 6                      # Resume a session
    RECONNECT = 7                   # Reconnect request
    REQUEST_MEMBERS = 8             # Request guild members
    HELLO = 10                      # Initial handshake
    HEARTBEAT_ACK = 11              # Heartbeat acknowledgment
    GUILD_SUBSCRIBE = 14            # Subscribe to guild (third-party)
    REQUEST_CHANNEL_STATUSES = 36   # Request channel statuses
    BULK_GUILD_SUBSCRIBE = 37       # Bulk guild subscription (web client)
    QOS_HEARTBEAT = 40              # Quality of service heartbeat


class GatewayEvent(Enum):
    """
    Discord Gateway Events.

    These represent the different types of events that can be received
    from the Gateway API.
    """
    # Debug/logging events
    RAW = '__RAW_DEBUG__'

    # Core connection events
    READY = 'READY'
    READY_SUPPLEMENTAL = 'READY_SUPPLEMENTAL'

    # Guild events
    GUILD_CREATE = 'GUILD_CREATE'
    GUILD_MEMBERS_CHUNK = 'GUILD_MEMBERS_CHUNK'
    GUILD_MEMBER_LIST_UPDATE = 'GUILD_MEMBER_LIST_UPDATE'

    # User/presence events
    PRESENCE_UPDATE = 'PRESENCE_UPDATE'
    TYPING_START = 'TYPING_START'

    # Custom events for internal handling
    RECONNECT = 'RECONNECT'
    SUBSCRIBE = 'SUBSCRIBE'


class GatewayError(Exception):
    """Base exception for gateway-related errors."""
    pass


class ConnectionError(GatewayError):
    """Exception raised when connection fails."""
    pass


class AuthenticationError(GatewayError):
    """Exception raised when authentication fails."""
    pass


class DiscordGatewayClient:
    """
    WebSocket client for Discord Gateway focused on presence collection.

    This client handles the WebSocket connection to Discord's Gateway API,
    managing authentication, heartbeats, compression, and event dispatching.
    It's specifically optimized for presence scraping and member list monitoring.

    Attributes:
        token: Discord authentication token
        websocket: Active WebSocket connection
        session_id: Current session ID for resumption
        sequence: Last sequence number received
    """

    def __init__(self, token: str, *, enable_compression: bool = True, debug_logging: bool = False):
        """
        Initialize the Discord Gateway client.

        Args:
            token: Discord authentication token
            enable_compression: Whether to enable zlib compression
            debug_logging: Whether to enable debug logging to file

        Raises:
            ValueError: If token is empty
        """
        if not token:
            raise ValueError("Token cannot be empty")

        self.token = token
        self.enable_compression = enable_compression
        self.debug_logging = debug_logging

        # WebSocket connection
        self.websocket: Optional[websockets.ClientConnection] = None
        self.session_id: Optional[str] = None
        self.sequence: int = 0

        # Heartbeat management
        self.heartbeat_interval: Optional[float] = None
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.last_heartbeat_ack: float = time.time()
        self.heartbeat_failures: int = 0

        # Event handling
        self.event_handlers: Dict[str, List[Callable]] = {}

        # Compression (zlib)
        self.inflator = zlib.decompressobj() if enable_compression else None
        self.buffer = bytearray()

        # Connection state
        self.connected = False
        self.ready = False
        self.reconnecting = False

        # Message processing
        self.processing_queue: asyncio.Queue = asyncio.Queue()
        self.processing_task: Optional[asyncio.Task] = None

        # Reconnection settings
        self.reconnect_url = ''
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5.0

        logger.debug(f"Initialized Gateway client (compression: {enable_compression})")
        
    def on(self, event_type: Union[str, GatewayEvent]):
        """
        Decorator for registering event handlers.

        Args:
            event_type: The event type to listen for

        Returns:
            Decorator function

        Example:
            @client.on(GatewayEvent.READY)
            async def on_ready(data):
                print("Client is ready!")
        """
        def decorator(func: Callable):
            event_name = str(event_type)
            if event_name not in self.event_handlers:
                self.event_handlers[event_name] = []
            self.event_handlers[event_name].append(func)
            logger.debug(f"Registered handler for event: {event_name}")
            return func
        return decorator

    async def _emit_event(self, event_type: Union[str, GatewayEvent], data: Any) -> None:
        """
        Emit an event to all registered handlers.

        Args:
            event_type: The event type being emitted
            data: Event data to pass to handlers
        """
        event_name = str(event_type)
        if event_name in self.event_handlers:
            for handler in self.event_handlers[event_name]:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(data)
                    else:
                        handler(data)
                except Exception as e:
                    logger.error(f"Error in event handler for {event_name}: {e}", exc_info=True)
    
    def _generate_identify_payload(self) -> Dict[str, Any]:
        """
        Generate IDENTIFY payload for initial authentication.

        This payload contains client information and capabilities
        needed to establish a Gateway session.

        Returns:
            IDENTIFY payload dictionary
        """
        return {
            "op": GatewayOPCodes.IDENTIFY,
            "d": {
                "token": self.token,
                "capabilities": 1734653,  # Full client capabilities for presence
                "properties": {
                    "os": "Windows",
                    "browser": "Chrome",
                    "device": "",
                    "browser_user_agent": (
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                        "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                    ),
                    "browser_version": "120.0.0.0",
                    "os_version": "10",
                    "referrer": "",
                    "referring_domain": "",
                    "release_channel": "stable",
                    "client_build_number": 448893,
                },
                "presence": {
                    "status": "online",
                    "since": 0,
                    "activities": [],
                    "afk": False
                },
                "compress": self.enable_compression,
                "client_state": {
                    "guild_versions": {}
                }
            }
        }
    
    def _generate_ranges(self, max_members: Union[int, None]) -> List[List[int]]:
        """
        Generate member ranges for guild subscriptions.

        Args:
            max_members: Maximum number of members, or None for default

        Returns:
            List of [start, end] ranges for member indices
        """
        n_ranges = max_members // 100 - 1 if isinstance(max_members, int) else 3

        ranges = []
        for i in range(n_ranges):
            ranges.append([100 * i, 100 * i + 99])

        return ranges

    async def _send_heartbeat(self) -> None:
        """
        Send heartbeat to maintain connection.

        Uses QOS_HEARTBEAT opcode which includes quality of service
        information for better connection monitoring.
        """
        if not self.websocket or self.websocket.state == websockets.State.CLOSED:
            logger.warning("Cannot send heartbeat: WebSocket not connected")
            return

        try:
            payload = {
                "op": GatewayOPCodes.QOS_HEARTBEAT,
                "d": {
                    'seq': self.sequence,
                    'qos': {
                        'active': True,
                        'ver': 26,
                        'reasons': []
                    }
                }
            }
            await self.websocket.send(json.dumps(payload))
            logger.debug(f"Sent heartbeat (seq: {self.sequence})")

        except Exception as e:
            logger.error(f"Failed to send heartbeat: {e}")
            self.heartbeat_failures += 1

            # Trigger reconnection if too many failures
            if self.heartbeat_failures >= 3:
                logger.warning("Too many heartbeat failures, triggering reconnection")
                await self._emit_event(GatewayEvent.RECONNECT, None)
    
    async def _heartbeat_loop(self, interval: float) -> None:
        """
        Heartbeat loop to keep connection alive.

        Sends periodic heartbeats and monitors for acknowledgments.
        Triggers reconnection if heartbeat ACKs are not received.
        """
        logger.debug(f"Starting heartbeat loop with interval: {interval * 1000:.0f}ms")

        while self.connected:
            try:
                # Wait for heartbeat interval
                await asyncio.sleep(interval)

                # Check if we received heartbeat ACK within reasonable time
                ack_timeout = interval * 2
                if time.time() - self.last_heartbeat_ack > ack_timeout:
                    logger.warning(f"Heartbeat ACK timeout ({ack_timeout:.0f}ms), triggering reconnection")
                    await self._emit_event(GatewayEvent.RECONNECT, None)
                    return

                # Send heartbeat
                await self._send_heartbeat()

            except asyncio.CancelledError:
                logger.debug("Heartbeat loop cancelled")
                return
            except Exception as e:
                logger.error(f"Error in heartbeat loop: {e}")
                await asyncio.sleep(1)  # Brief pause before retrying
    
    def _decompress_message(self, data: bytes) -> Optional[str]:
        """
        Decompress zlib-compressed message from Discord.

        Discord uses zlib compression with a specific suffix pattern
        to indicate complete messages.

        Args:
            data: Raw compressed bytes from WebSocket

        Returns:
            Decompressed message string, or None if incomplete
        """
        if not self.enable_compression or not self.inflator:
            # If compression is disabled, treat as plain text
            try:
                return data.decode('utf-8')
            except UnicodeDecodeError as e:
                logger.error(f"Failed to decode message: {e}")
                return None

        self.buffer.extend(data)

        # Check if we have a complete message (ends with 0x0000ffff)
        if len(self.buffer) < 4 or self.buffer[-4:] != b'\x00\x00\xff\xff':
            return None

        try:
            decompressed = self.inflator.decompress(self.buffer)
            self.buffer.clear()
            message = decompressed.decode('utf-8')
            logger.debug(f"Decompressed message: {len(message)} characters")
            return message

        except Exception as e:
            logger.error(f"Decompression error: {e}")
            logger.debug(f"Failed data: {data[:100]}...")  # Log first 100 bytes
            self.buffer.clear()
            return None
    
    async def _handle_message(self, message: str) -> None:
        """
        Handle incoming WebSocket message.

        Parses the message and dispatches appropriate events based on
        the operation code and event type.

        Args:
            message: Raw JSON message from WebSocket
        """
        # Optional debug logging to file
        if self.debug_logging:
            try:
                with open('full_logs.txt', 'a', encoding='utf-8') as file:
                    file.write(message + '\n')
            except Exception as e:
                logger.warning(f"Failed to write debug log: {e}")

        try:
            data = json.loads(message)
            op = data.get("op")
            d = data.get("d")
            t = data.get("t")
            s = data.get("s")

            # Update sequence number for session resumption
            if s is not None:
                self.sequence = s
                logger.debug(f"Updated sequence to {s}")

            # Handle different operation codes
            await self._handle_opcode(op, d, t)

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse message as JSON: {e}")
            logger.debug(f"Invalid message: {message[:200]}...")
        except Exception as e:
            logger.error(f"Error handling message: {e}", exc_info=True)

    async def _handle_opcode(self, op: int, d: Any, t: Optional[str]) -> None:
        """
        Handle specific operation codes.

        Args:
            op: Operation code
            d: Data payload
            t: Event type (for DISPATCH events)
        """
        if op == GatewayOPCodes.HELLO:
            await self._handle_hello(d)

        elif op == GatewayOPCodes.HEARTBEAT_ACK:
            self.last_heartbeat_ack = time.time()
            self.heartbeat_failures = 0  # Reset failure counter
            logger.debug("Received heartbeat ACK")

        elif op == GatewayOPCodes.RECONNECT:
            logger.info("Received reconnect request from Discord")
            await self._emit_event(GatewayEvent.RECONNECT, d)

        elif op == GatewayOPCodes.DISPATCH:
            await self._handle_dispatch_event(t, d)

        else:
            logger.debug(f"Unhandled opcode {op}")

    async def _handle_hello(self, data: Dict[str, Any]) -> None:
        """
        Handle HELLO event and start heartbeat.
        """
        heartbeat_interval = data["heartbeat_interval"] / 1000.0  # Convert to seconds
        logger.info(f"Received HELLO, heartbeat interval: {heartbeat_interval * 1000:.0f}ms")

        # Start heartbeat task
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop(heartbeat_interval))

        # Check if we should resume or identify
        if self.sequence > 5 and self.session_id:
            logger.info("Attempting to resume session")
            await self._send_resume_payload()
            await self._emit_event(GatewayEvent.RAW, {'debug': 'Sent Resume Payload'})
            await asyncio.sleep(5)
            await self._emit_event(GatewayEvent.SUBSCRIBE, None)
            await self._emit_event(GatewayEvent.RAW, {'debug': 'Sent Subscription'})
        else:
            logger.info("Identifying with Discord")
            identify_payload = self._generate_identify_payload()
            await self.websocket.send(json.dumps(identify_payload))
            logger.debug("IDENTIFY payload sent, waiting for READY event...")
    async def _handle_dispatch_event(self, event_type: Optional[str], data: Any) -> None:
        """
        Handle DISPATCH events.

        Args:
            event_type: The event type (e.g., 'READY', 'GUILD_CREATE')
            data: Event data payload
        """
        if not event_type:
            logger.warning("Received DISPATCH with no event type")
            return

        logger.debug(f"Handling dispatch event: {event_type}")

        # Handle specific events that need special processing
        if event_type == "READY":
            self.session_id = data["session_id"]
            self.ready = True
            logger.info(f"Gateway ready! Session ID: {self.session_id}")
            await self._emit_event(GatewayEvent.READY, data)

        elif event_type == "READY_SUPPLEMENTAL":
            logger.info("Received READY_SUPPLEMENTAL")
            await self._emit_event(GatewayEvent.READY_SUPPLEMENTAL, data)

        elif event_type == "GUILD_CREATE":
            guild_id = data.get("id", "unknown")
            logger.debug(f"Guild created/available: {guild_id}")
            await self._emit_event(GatewayEvent.GUILD_CREATE, data)

        elif event_type == "GUILD_MEMBERS_CHUNK":
            guild_id = data.get("guild_id", "unknown")
            chunk_index = data.get("chunk_index", 0)
            chunk_count = data.get("chunk_count", 1)
            logger.debug(f"Received members chunk {chunk_index + 1}/{chunk_count} for guild {guild_id}")
            await self._emit_event(GatewayEvent.GUILD_MEMBERS_CHUNK, data)

        elif event_type == "GUILD_MEMBER_LIST_UPDATE":
            guild_id = data.get("guild_id", "unknown")
            ops_count = len(data.get("ops", []))
            logger.debug(f"Member list update for guild {guild_id}: {ops_count} operations")
            await self._emit_event(GatewayEvent.GUILD_MEMBER_LIST_UPDATE, data)

        elif event_type == "PRESENCE_UPDATE":
            user_id = data.get("user", {}).get("id", "unknown")
            logger.debug(f"Presence update for user {user_id}")
            await self._emit_event(GatewayEvent.PRESENCE_UPDATE, data)

        elif event_type == "TYPING_START":
            # Usually not needed for presence scraping, but available
            await self._emit_event(GatewayEvent.TYPING_START, data)

        else:
            # Handle unknown events
            logger.debug(f"Unknown dispatch event: {event_type}")
            await self._emit_event(event_type, data)

        # Always emit raw event for debugging/logging
        await self._emit_event(GatewayEvent.RAW, {"type": event_type, "data": data})
    
    async def connect(self, gateway_url: str = "wss://gateway.discord.gg/", *, use_compression: bool = True) -> None:
        """
        Connect to Discord Gateway.

        Args:
            gateway_url: WebSocket URL for the gateway
            use_compression: Whether to request compression

        Raises:
            ConnectionError: If connection fails
            AuthenticationError: If authentication fails
        """
        # Build connection URL with parameters
        params = ["v=9", "encoding=json"]
        if use_compression and self.enable_compression:
            params.append("compress=zlib-stream")

        url = f"{gateway_url}?{'&'.join(params)}"
        logger.info(f"Connecting to Discord Gateway: {url}")

        try:
            # Connect to WebSocket
            self.websocket = await websockets.connect(
                url,
                max_size=None,  # No message size limit
                compression=None,  # We handle compression manually
                ping_interval=None,  # We handle heartbeats manually
                ping_timeout=None
            )

            self.connected = True
            self.reconnecting = False
            logger.info("WebSocket connection established")

            # Start message processing
            await self._start_message_processing()

        except Exception as e:
            logger.error(f"WebSocket connection error: {e}")
            self.connected = False
            raise ConnectionError(f"Failed to connect to Discord Gateway: {e}")

    async def _start_message_processing(self) -> None:
        """
        Start the message processing loops.

        This starts both the WebSocket listener and message processor.
        """
        try:
            # Start processing task
            if self.processing_task:
                self.processing_task.cancel()
            self.processing_task = asyncio.create_task(self._message_loop())

            # Start WebSocket listener
            await self._websocket_loop()

        except Exception as e:
            logger.error(f"Error in message processing: {e}")
            raise
    
    async def _websocket_loop(self) -> None:
        """
        WebSocket message listener loop.

        Listens for incoming messages and queues them for processing.
        Handles connection errors and triggers reconnection when needed.
        """
        try:
            async for message in self.websocket:
                await self.processing_queue.put(message)

        except ConnectionClosed as e:
            logger.warning(f"WebSocket connection closed: {e}")
            if not self.reconnecting:
                await self.reconnect()

        except WebSocketException as e:
            logger.error(f"WebSocket error: {e}")
            self.connected = False
            if not self.reconnecting:
                await self.reconnect()

        except Exception as e:
            logger.error(f"Unexpected error in WebSocket loop: {e}")
            self.connected = False

    async def _message_loop(self) -> None:
        """
        Message processing loop.

        Processes queued messages from the WebSocket, handling both
        compressed and uncompressed data.
        """
        logger.debug("Starting message processing loop")

        while self.connected:
            try:
                # Get message from queue with timeout
                message = await asyncio.wait_for(
                    self.processing_queue.get(),
                    timeout=30.0
                )

                if isinstance(message, bytes):
                    # Handle compressed message
                    decompressed = self._decompress_message(message)
                    if decompressed:
                        await self._handle_message(decompressed)
                else:
                    # Handle text message
                    await self._handle_message(message)

            except asyncio.TimeoutError:
                # No message received in 30 seconds, continue
                continue

            except asyncio.CancelledError:
                logger.debug("Message loop cancelled")
                return

            except Exception as e:
                logger.error(f"Error processing message: {e}", exc_info=True)

    async def _send_resume_payload(self) -> None:
        """
        Send RESUME payload to resume an existing session.

        This is used when reconnecting to avoid losing state
        and having to re-identify.
        """
        if not self.session_id:
            logger.warning("Cannot resume: no session ID available")
            return

        logger.info(f"Sending resume payload (session: {self.session_id}, seq: {self.sequence})")

        payload = {
            "op": GatewayOPCodes.RESUME,
            "d": {
                "token": self.token,
                "session_id": self.session_id,
                "seq": self.sequence
            }
        }

        try:
            await self.websocket.send(json.dumps(payload))
            logger.debug("Resume payload sent successfully")
        except Exception as e:
            logger.error(f"Failed to send resume payload: {e}")
            raise

    async def reconnect(self) -> None:
        """
        Reconnect to the gateway with exponential backoff.

        Attempts to resume the session if possible, otherwise
        starts a new session.
        """
        if self.reconnecting:
            logger.debug("Reconnection already in progress")
            return

        self.reconnecting = True
        logger.info("Starting reconnection process")

        # Clear compression buffer
        self.buffer.clear()
        await self._emit_event(GatewayEvent.RAW, {'debug': 'Reconnection starts'})

        # Disconnect cleanly
        await self.disconnect()

        # Attempt reconnection with exponential backoff
        for attempt in range(self.max_reconnect_attempts):
            try:
                delay = min(self.reconnect_delay * (2 ** attempt), 60)  # Max 60s delay
                logger.info(f"Reconnection attempt {attempt + 1}/{self.max_reconnect_attempts} (delay: {delay}s)")

                if attempt > 0:
                    await asyncio.sleep(delay)

                # Use reconnect URL if available, otherwise fallback to default
                url = self.reconnect_url or "wss://gateway.discord.gg/"
                await self.connect(url)

                logger.info("Successfully reconnected")
                await self._emit_event(GatewayEvent.RAW, {'debug': 'Reconnected successfully'})
                return

            except Exception as e:
                logger.warning(f"Reconnection attempt {attempt + 1} failed: {e}")

                if attempt == self.max_reconnect_attempts - 1:
                    logger.error("All reconnection attempts failed")
                    self.reconnecting = False
                    raise ConnectionError("Failed to reconnect after maximum attempts")

        self.reconnecting = False
    
    async def close(self) -> None:
        """
        Close the gateway connection and cleanup resources.
        """
        logger.info("Closing gateway connection")
        
        self.connected = False
        self.maintain_connection = False
        
        # Cancel processing task
        if self.processing_task and not self.processing_task.done():
            self.processing_task.cancel()
            try:
                await self.processing_task
            except asyncio.CancelledError:
                logger.debug("Message loop cancelled")
        
        # Cancel heartbeat task
        if self.heartbeat_task and not self.heartbeat_task.done():
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                logger.debug("Heartbeat loop cancelled")
        
        # Close WebSocket connection
        if self.websocket and not self.websocket.closed:
            await self.websocket.close()
        
        logger.debug("Gateway connection closed")
    
    async def bulk_guild_subscribe(self, subscriptions: Dict[str, Dict[str, Any]]) -> None:
        """
        Subscribe to multiple guilds at once (OP 37).

        This is the primary method used by the Discord web client for
        subscribing to guild member lists and presence updates.

        Args:
            subscriptions: Dict mapping guild_id to subscription settings

        Raises:
            ConnectionError: If WebSocket is not connected
        """
        if not self.websocket or not self.connected:
            raise ConnectionError("WebSocket not connected")

        payload = {
            "op": GatewayOPCodes.BULK_GUILD_SUBSCRIBE,
            "d": {
                "subscriptions": subscriptions
            }
        }

        try:
            await self.websocket.send(json.dumps(payload))
            logger.debug(f"Sent bulk guild subscription for {len(subscriptions)} guilds")
        except Exception as e:
            logger.error(f"Failed to send bulk guild subscription: {e}")
            raise

    async def _generate_subscription_payload(self, **kwargs) -> Dict[str, Any]:
        """
        Generate subscription payload for guild subscriptions.

        Args:
            guild_id: Guild ID to subscribe to
            typing: Enable typing events
            activities: Enable activity/presence events
            threads: Enable thread events
            member_updates: Enable member update events
            channels: List of channel IDs to subscribe to
            ranges: List of member index ranges

        Returns:
            Subscription payload dictionary
        """
        guild_id = str(kwargs.get('guild_id'))

        subscriptions = {
            guild_id: {}
        }

        # Add optional subscription types
        if kwargs.get('typing') is not None:
            subscriptions[guild_id]['typing'] = kwargs['typing']
        if kwargs.get('activities') is not None:
            subscriptions[guild_id]['activities'] = kwargs['activities']
        if kwargs.get('threads') is not None:
            subscriptions[guild_id]['threads'] = kwargs['threads']
        if kwargs.get('member_updates') is not None:
            subscriptions[guild_id]['member_updates'] = kwargs['member_updates']

        # Generate member ranges
        subranges = [[0, 99]]  # Always include first 100 members
        if kwargs.get('ranges') is not None:
            subranges.extend([[i*100, i*100+99] for i in kwargs['ranges'] if i != 0])

        # Add channel subscriptions
        if kwargs.get('channels') is not None:
            subscriptions[guild_id]['channels'] = {}
            for channel in kwargs['channels']:
                subscriptions[guild_id]['channels'][str(channel)] = subranges

        logger.debug(f"Generated subscription for guild {guild_id} with {len(subranges)} ranges")
        return subscriptions
    
    async def subscribe_to_guild(
        self,
        guild_id: Union[str, int],
        *,
        typing: Optional[bool] = None,
        activities: Optional[bool] = None,
        threads: Optional[bool] = None,
        member_updates: Optional[bool] = None,
        channels: Optional[List[Union[int, str]]] = None,
        ranges: Optional[List[int]] = None
    ) -> None:
        """
        Subscribe to a single guild's events.

        This is a convenience method that wraps bulk_guild_subscribe
        for single guild subscriptions.

        Args:
            guild_id: Guild to subscribe to
            typing: Receive typing events
            activities: Receive activity/presence updates
            threads: Receive thread events
            member_updates: Receive member update events
            channels: Channel IDs to subscribe to (max 5 recommended)
            ranges: List of member index ranges (max 3, must include 0)

        Raises:
            ConnectionError: If WebSocket is not connected
            ValueError: If parameters are invalid
        """
        if channels and len(channels) > 5:
            logger.warning(f"Subscribing to {len(channels)} channels, Discord recommends max 5")

        if ranges and len(ranges) > 3:
            raise ValueError("Maximum 3 ranges allowed per subscription")

        subscriptions = await self._generate_subscription_payload(
            guild_id=guild_id,
            typing=typing,
            activities=activities,
            threads=threads,
            member_updates=member_updates,
            channels=channels,
            ranges=ranges
        )

        await self.bulk_guild_subscribe(subscriptions)

    async def request_channel_statuses(self, guild_id: Union[str, int]) -> None:
        """
        Request channel statuses for a guild (OP 36).

        This is not strictly necessary for getting data, but it replicates
        what the normal Discord client sends before and after OP 37.

        Args:
            guild_id: Guild ID to request statuses for

        Raises:
            ConnectionError: If WebSocket is not connected
        """
        if not self.websocket or not self.connected:
            raise ConnectionError("WebSocket not connected")

        payload = {
            "op": GatewayOPCodes.REQUEST_CHANNEL_STATUSES,
            "d": {
                "guild_id": str(guild_id)
            }
        }

        try:
            await self.websocket.send(json.dumps(payload))
            logger.debug(f"Requested channel statuses for guild {guild_id}")
        except Exception as e:
            logger.error(f"Failed to request channel statuses: {e}")
            raise

    # Context manager support

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, _exc_type, _exc_val, _exc_tb):
        """Async context manager exit."""
        await self.close()

