"""
Discord HTTP Client Module

This module provides a robust HTTP client for interacting with the Discord API.
It handles authentication, rate limiting, browser emulation, and error handling.

Features:
- Automatic rate limit handling with exponential backoff
- Browser fingerprinting for better API compatibility
- Comprehensive error handling and logging
- Async/await support for high performance
- Type hints for better code maintainability
"""

import asyncio
import base64
import json
import logging
import re
import time
from typing import Dict, Any, Optional, List, Union

import httpx


logger = logging.getLogger(__name__)


class DiscordAPIError(Exception):
    """Base exception for Discord API errors."""

    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class RateLimitError(DiscordAPIError):
    """Exception raised when rate limit is exceeded."""

    def __init__(self, retry_after: float, message: str = "Rate limit exceeded"):
        super().__init__(message)
        self.retry_after = retry_after


class AuthenticationError(DiscordAPIError):
    """Exception raised when authentication fails."""
    pass


class PermissionError(DiscordAPIError):
    """Exception raised when insufficient permissions."""
    pass


class DiscordHTTPClient:
    """
    Async HTTP client for Discord API interactions.

    This client handles authentication, rate limiting, and browser emulation
    to provide reliable access to Discord's API endpoints.

    Attributes:
        BASE_URL: Discord API base URL
        token: Discord authentication token
    """

    BASE_URL = "https://discord.com/api/v9"

    # Discord client build numbers for different versions
    CLIENT_BUILD_NUMBERS = {
        'stable': 270518,
        'ptb': 270519,
        'canary': 270520
    }

    def __init__(self, token: str, *, client_type: str = 'stable', timeout: float = 30.0):
        """
        Initialize the Discord HTTP client.

        Args:
            token: Discord authentication token (with or without 'Bot ' prefix)
            client_type: Client type for build number ('stable', 'ptb', 'canary')
            timeout: Request timeout in seconds

        Raises:
            ValueError: If token is empty or client_type is invalid
        """
        if not token:
            raise ValueError("Token cannot be empty")

        if client_type not in self.CLIENT_BUILD_NUMBERS:
            raise ValueError(f"Invalid client_type. Must be one of: {list(self.CLIENT_BUILD_NUMBERS.keys())}")

        # Ensure token has proper format
        self.token = token if token.startswith(('Bot ', 'Bearer ')) else token
        self.client_type = client_type
        self.timeout = timeout

        # HTTP session and rate limiting
        self._session: Optional[httpx.AsyncClient] = None
        self._rate_limits: Dict[str, float] = {}
        self._global_rate_limit: float = 0.0

        # Browser fingerprinting data
        self._super_properties = self._generate_super_properties()

        logger.debug(f"Initialized Discord HTTP client with {client_type} build")
    
    def _generate_super_properties(self) -> str:
        """
        Generate X-Super-Properties header for browser emulation.

        This header contains client information that Discord uses for
        analytics and compatibility checking.

        Returns:
            Base64-encoded JSON string containing client properties
        """
        properties = {
            "os": "Windows",
            "browser": "Chrome",
            "device": "",
            "system_locale": "en-US",
            "browser_user_agent": (
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ),
            "browser_version": "120.0.0.0",
            "os_version": "10",
            "referrer": "",
            "referring_domain": "",
            "referrer_current": "",
            "referring_domain_current": "",
            "release_channel": self.client_type,
            "client_build_number": self.CLIENT_BUILD_NUMBERS[self.client_type],
            "client_event_source": None
        }

        # Base64 encode the properties
        properties_json = json.dumps(properties, separators=(',', ':'))
        encoded = base64.b64encode(properties_json.encode()).decode()

        logger.debug("Generated super properties for browser emulation")
        return encoded
    
    def _get_headers(self) -> Dict[str, str]:
        """
        Get headers for Discord API requests.

        These headers emulate a real browser to avoid detection and
        ensure compatibility with Discord's API.

        Returns:
            Dictionary of HTTP headers
        """
        return {
            "Authorization": self.token,
            "User-Agent": (
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ),
            "X-Super-Properties": self._super_properties,
            "X-Discord-Locale": "en-US",
            "X-Discord-Timezone": "America/New_York",
            "X-Debug-Options": "bugReporterEnabled",
            "Accept": "*/*",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "Sec-CH-UA": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            "Sec-CH-UA-Mobile": "?0",
            "Sec-CH-UA-Platform": '"Windows"'
        }
    
    async def _ensure_session(self) -> None:
        """
        Ensure HTTP session is created and configured.

        Creates a new httpx.AsyncClient with appropriate headers,
        timeout, and connection settings if one doesn't exist.
        """
        if self._session is None:
            self._session = httpx.AsyncClient(
                headers=self._get_headers(),
                timeout=self.timeout,
                follow_redirects=True,
                limits=httpx.Limits(max_keepalive_connections=10, max_connections=20)
            )
            logger.debug("Created new HTTP session")

    async def _handle_rate_limit(self, route: str) -> None:
        """
        Handle rate limiting for a specific route.

        Checks both global and route-specific rate limits and waits
        if necessary before allowing the request to proceed.

        Args:
            route: The API route being accessed (e.g., "GET:/users/@me")
        """
        current_time = time.time()

        # Check global rate limit
        if self._global_rate_limit > current_time:
            wait_time = self._global_rate_limit - current_time
            logger.warning(f"Global rate limit hit, waiting {wait_time:.2f}s")
            await asyncio.sleep(wait_time)

        # Check route-specific rate limit
        if route in self._rate_limits:
            wait_time = self._rate_limits[route] - current_time
            if wait_time > 0:
                logger.warning(f"Route rate limit hit for {route}, waiting {wait_time:.2f}s")
                await asyncio.sleep(wait_time)
    
    async def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        Make a request to Discord API with comprehensive error handling.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE, etc.)
            endpoint: API endpoint path (e.g., "/users/@me")
            **kwargs: Additional arguments passed to httpx.request()

        Returns:
            Parsed JSON response data

        Raises:
            AuthenticationError: If token is invalid (401)
            PermissionError: If insufficient permissions (403)
            RateLimitError: If rate limit exceeded and retries failed
            DiscordAPIError: For other API errors
        """
        await self._ensure_session()

        url = f"{self.BASE_URL}{endpoint}"
        route = f"{method}:{endpoint}"

        logger.debug(f"Making {method} request to {endpoint}")

        # Handle rate limiting
        await self._handle_rate_limit(route)

        try:
            response = await self._session.request(method, url, **kwargs)

            # Handle rate limit headers
            self._update_rate_limits(response, route)

            # Handle rate limit exceeded
            if response.status_code == 429:
                retry_data = response.json()
                retry_after = retry_data.get("retry_after", 1)
                is_global = retry_data.get("global", False)

                logger.warning(
                    f"Rate limit exceeded for {route}. "
                    f"Retrying after {retry_after}s (global: {is_global})"
                )

                if is_global:
                    self._global_rate_limit = time.time() + retry_after
                else:
                    self._rate_limits[route] = time.time() + retry_after

                await asyncio.sleep(retry_after)
                return await self._request(method, endpoint, **kwargs)

            response.raise_for_status()

            # Clean and parse response text
            text = self._clean_response_text(response.text)
            logger.debug(f"Received response from {endpoint}: {len(text)} characters")

            return json.loads(text)

        except httpx.HTTPStatusError as e:
            await self._handle_http_error(e, route)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response from {endpoint}: {e}")
            raise DiscordAPIError(f"Invalid JSON response: {e}")
        except Exception as e:
            logger.error(f"Unexpected error during request to {endpoint}: {e}")
            raise DiscordAPIError(f"Request failed: {e}")

    def _update_rate_limits(self, response: httpx.Response, route: str) -> None:
        """
        Update rate limit information from response headers.

        Args:
            response: HTTP response object
            route: API route that was accessed
        """
        if "X-RateLimit-Reset-After" in response.headers:
            try:
                reset_after = float(response.headers["X-RateLimit-Reset-After"])
                self._rate_limits[route] = time.time() + reset_after
                logger.debug(f"Updated rate limit for {route}: {reset_after}s")
            except (ValueError, TypeError):
                logger.warning(f"Invalid rate limit header value: {response.headers['X-RateLimit-Reset-After']}")

    def _clean_response_text(self, text: str) -> str:
        """
        Clean response text by removing non-ASCII characters.

        Args:
            text: Raw response text

        Returns:
            Cleaned text with only ASCII characters
        """
        # Remove non-ASCII characters that might cause JSON parsing issues
        return re.sub(r'[^\x00-\x7F]', '', text)

    async def _handle_http_error(self, error: httpx.HTTPStatusError, route: str) -> None:
        """
        Handle HTTP errors and raise appropriate exceptions.

        Args:
            error: HTTP status error from httpx
            route: API route that failed

        Raises:
            AuthenticationError: For 401 errors
            PermissionError: For 403 errors
            DiscordAPIError: For other HTTP errors
        """
        status_code = error.response.status_code

        try:
            error_data = error.response.json()
            error_message = error_data.get('message', 'Unknown error')
            error_code = error_data.get('code', 0)
        except:
            error_data = {}
            error_message = error.response.text or f"HTTP {status_code}"
            error_code = 0

        logger.error(f"HTTP {status_code} error for {route}: {error_message} (code: {error_code})")

        if status_code == 401:
            raise AuthenticationError("Invalid or expired token", status_code, error_data)
        elif status_code == 403:
            raise PermissionError("Insufficient permissions for this action", status_code, error_data)
        else:
            raise DiscordAPIError(f"HTTP {status_code}: {error_message}", status_code, error_data)
    
    # API Methods

    async def get_me(self) -> Dict[str, Any]:
        """
        Get current user information.

        Returns:
            User object containing id, username, discriminator, etc.

        Raises:
            AuthenticationError: If token is invalid
        """
        return await self._request("GET", "/users/@me")

    async def get_gateway(self) -> Dict[str, Any]:
        """
        Get WebSocket gateway URL and connection information.

        Returns:
            Gateway object containing URL and session start limit info
        """
        return await self._request("GET", "/gateway")

    async def get_guild(self, guild_id: Union[str, int]) -> Dict[str, Any]:
        """
        Get guild information.

        Args:
            guild_id: Guild ID to fetch information for

        Returns:
            Guild object with detailed information

        Raises:
            PermissionError: If not a member of the guild or insufficient permissions
        """
        return await self._request("GET", f"/guilds/{guild_id}")

    async def get_guild_members(
        self,
        guild_id: Union[str, int],
        limit: int = 1000,
        after: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get guild members (requires appropriate permissions).

        Args:
            guild_id: Guild ID to fetch members from
            limit: Maximum number of members to return (1-1000)
            after: Get members after this user ID (for pagination)

        Returns:
            List of member objects

        Raises:
            PermissionError: If insufficient permissions to view members
        """
        if not 1 <= limit <= 1000:
            raise ValueError("Limit must be between 1 and 1000")

        params = {"limit": limit}
        if after:
            params["after"] = str(after)

        return await self._request("GET", f"/guilds/{guild_id}/members", params=params)

    async def get_guild_preview(self, guild_id: Union[str, int]) -> Dict[str, Any]:
        """
        Get guild preview information (public information).

        Args:
            guild_id: Guild ID to fetch preview for

        Returns:
            Guild preview object with public information
        """
        return await self._request("GET", f"/guilds/{guild_id}/preview")

    async def get_user(self, user_id: Union[str, int]) -> Dict[str, Any]:
        """
        Get user information by ID.

        Args:
            user_id: User ID to fetch information for

        Returns:
            User object
        """
        return await self._request("GET", f"/users/{user_id}")

    async def get_guild_channels(self, guild_id: Union[str, int]) -> List[Dict[str, Any]]:
        """
        Get channels in a guild.

        Args:
            guild_id: Guild ID to fetch channels from

        Returns:
            List of channel objects
        """
        return await self._request("GET", f"/guilds/{guild_id}/channels")

    # Session Management

    async def close(self) -> None:
        """
        Close the HTTP session and clean up resources.

        This should be called when the client is no longer needed
        to properly close connections and free resources.
        """
        if self._session:
            await self._session.aclose()
            self._session = None
            logger.debug("HTTP session closed")

    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
