"""
Utility Functions for Discord Presence Toolkit

This module contains common utility functions used throughout the toolkit
for data processing, file operations, and helper functions.
"""

import csv
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Union


logger = logging.getLogger(__name__)


class CSVWriter:
    """
    Buffered CSV writer for efficient output.
    
    This class buffers CSV rows in memory and writes them in batches
    to improve performance when writing large amounts of data.
    """
    
    def __init__(self, filepath: Union[str, Path], headers: List[str], buffer_size: int = 1000):
        """
        Initialize CSV writer.
        
        Args:
            filepath: Path to CSV file
            headers: Column headers
            buffer_size: Number of rows to buffer before writing
        """
        self.filepath = Path(filepath)
        self.headers = headers
        self.buffer_size = buffer_size
        self.buffer: List[List[Any]] = []
        self.total_rows_written = 0
        
        # Create directory if it doesn't exist
        self.filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # Write headers if file doesn't exist
        if not self.filepath.exists():
            self._write_headers()
    
    def _write_headers(self) -> None:
        """Write CSV headers to file."""
        with open(self.filepath, 'w', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            writer.writerow(self.headers)
        logger.debug(f"Wrote CSV headers to {self.filepath}")
    
    def add_row(self, row: List[Any]) -> None:
        """
        Add a row to the buffer.
        
        Args:
            row: Row data to add
        """
        self.buffer.append(row)
        
        if len(self.buffer) >= self.buffer_size:
            self.flush()
    
    def add_rows(self, rows: List[List[Any]]) -> None:
        """
        Add multiple rows to the buffer.
        
        Args:
            rows: List of rows to add
        """
        for row in rows:
            self.add_row(row)
    
    def flush(self) -> None:
        """Write buffered rows to file."""
        if not self.buffer:
            return
        
        try:
            with open(self.filepath, 'a', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerows(self.buffer)
            
            self.total_rows_written += len(self.buffer)
            logger.debug(f"Wrote {len(self.buffer)} rows to CSV (total: {self.total_rows_written})")
            self.buffer.clear()
            
        except Exception as e:
            logger.error(f"Failed to write CSV data: {e}")
            raise
    
    def close(self) -> None:
        """Flush remaining data and close writer."""
        self.flush()
        logger.info(f"CSV writer closed. Total rows written: {self.total_rows_written}")


class JSONLogger:
    """
    JSON logger for raw Discord data.
    
    This class handles logging raw JSON data from Discord's API
    for debugging and analysis purposes.
    """
    
    def __init__(self, filepath: Union[str, Path]):
        """
        Initialize JSON logger.
        
        Args:
            filepath: Path to log file
        """
        self.filepath = Path(filepath)
        self.filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # Open file in append mode
        self.file = open(self.filepath, 'a', encoding='utf-8')
        logger.debug(f"Opened JSON log file: {self.filepath}")
    
    def log(self, data: Dict[str, Any]) -> None:
        """
        Log JSON data.
        
        Args:
            data: Data to log
        """
        try:
            json_line = json.dumps(data, separators=(',', ':'))
            self.file.write(json_line + '\n')
            self.file.flush()  # Ensure data is written immediately
        except Exception as e:
            logger.error(f"Failed to write JSON log: {e}")
    
    def close(self) -> None:
        """Close the log file."""
        if self.file and not self.file.closed:
            self.file.close()
            logger.debug("JSON log file closed")


def filter_accessible_channels(guild_data: Dict[str, Any]) -> List[str]:
    """
    Filter channels that are accessible to @everyone.
    
    Args:
        guild_data: Raw guild data from Discord
        
    Returns:
        List of accessible channel IDs
    """
    guild_id = guild_data['properties']['id']
    channels = guild_data.get('channels', [])
    accessible_channels = []
    
    for channel in channels:
        # Only process text channels (type 0)
        if channel.get('type') != 0:
            continue
        
        # Check permissions
        permissions = channel.get('permission_overwrites', [])
        everyone_permission = None
        
        # Find @everyone role permission (role ID equals guild ID)
        for perm in permissions:
            if perm.get('id') == guild_id:
                everyone_permission = perm
                break
        
        # If @everyone has explicit deny for view channel (1024), skip
        if everyone_permission:
            deny = int(everyone_permission.get('deny', 0))
            if deny & 1024 != 0:  # VIEW_CHANNEL permission
                continue
        
        accessible_channels.append(str(channel['id']))
    
    logger.debug(f"Found {len(accessible_channels)} accessible channels in guild {guild_id}")
    return accessible_channels


def generate_member_ranges(max_members: Optional[int] = None, range_size: int = 100) -> List[List[int]]:
    """
    Generate member ranges for guild subscriptions.
    
    Args:
        max_members: Maximum number of members, or None for default
        range_size: Size of each range
        
    Returns:
        List of [start, end] ranges
    """
    if max_members is None:
        # Default to 3 ranges
        n_ranges = 3
    else:
        n_ranges = max(1, max_members // range_size)
    
    ranges = []
    for i in range(n_ranges):
        start = i * range_size
        end = start + range_size - 1
        ranges.append([start, end])
    
    return ranges


def calculate_rate_limit_delay(remaining: int, reset_after: float) -> float:
    """
    Calculate appropriate delay for rate limiting.
    
    Args:
        remaining: Number of requests remaining
        reset_after: Time until rate limit resets
        
    Returns:
        Delay in seconds
    """
    if remaining <= 0:
        return reset_after
    
    # Add small buffer to avoid hitting limits
    return (reset_after / remaining) * 1.1


def format_duration(seconds: float) -> str:
    """
    Format duration in human-readable format.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"


def format_number(number: int) -> str:
    """
    Format number with thousands separators.
    
    Args:
        number: Number to format
        
    Returns:
        Formatted number string
    """
    return f"{number:,}"


def safe_get_nested(data: Dict[str, Any], *keys: str, default: Any = None) -> Any:
    """
    Safely get nested dictionary value.
    
    Args:
        data: Dictionary to search
        *keys: Nested keys to traverse
        default: Default value if key not found
        
    Returns:
        Value at nested key or default
    """
    current = data
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return default
    return current


def chunk_list(items: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    Split list into chunks of specified size.
    
    Args:
        items: List to chunk
        chunk_size: Size of each chunk
        
    Returns:
        List of chunks
    """
    chunks = []
    for i in range(0, len(items), chunk_size):
        chunks.append(items[i:i + chunk_size])
    return chunks


def validate_discord_id(discord_id: Union[str, int]) -> bool:
    """
    Validate Discord ID format.
    
    Args:
        discord_id: ID to validate
        
    Returns:
        True if valid Discord ID
    """
    try:
        id_int = int(discord_id)
        # Discord IDs are 64-bit integers (snowflakes)
        return 0 < id_int < (1 << 64)
    except (ValueError, TypeError):
        return False


def get_file_size(filepath: Union[str, Path]) -> int:
    """
    Get file size in bytes.
    
    Args:
        filepath: Path to file
        
    Returns:
        File size in bytes, or 0 if file doesn't exist
    """
    try:
        return Path(filepath).stat().st_size
    except (OSError, FileNotFoundError):
        return 0


def ensure_directory(filepath: Union[str, Path]) -> None:
    """
    Ensure directory exists for given filepath.
    
    Args:
        filepath: File path to ensure directory for
    """
    Path(filepath).parent.mkdir(parents=True, exist_ok=True)


class ProgressTracker:
    """
    Simple progress tracking utility.
    """
    
    def __init__(self, total: Optional[int] = None, description: str = "Progress"):
        """
        Initialize progress tracker.
        
        Args:
            total: Total number of items (if known)
            description: Description of what's being tracked
        """
        self.total = total
        self.description = description
        self.current = 0
        self.start_time = time.time()
        self.last_log_time = self.start_time
        self.log_interval = 10.0  # Log every 10 seconds
    
    def update(self, increment: int = 1) -> None:
        """
        Update progress.
        
        Args:
            increment: Amount to increment by
        """
        self.current += increment
        
        # Log progress periodically
        now = time.time()
        if now - self.last_log_time >= self.log_interval:
            self._log_progress()
            self.last_log_time = now
    
    def _log_progress(self) -> None:
        """Log current progress."""
        elapsed = time.time() - self.start_time
        rate = self.current / elapsed if elapsed > 0 else 0
        
        if self.total:
            percentage = (self.current / self.total) * 100
            logger.info(f"{self.description}: {self.current}/{self.total} ({percentage:.1f}%) - {rate:.1f}/s")
        else:
            logger.info(f"{self.description}: {self.current} - {rate:.1f}/s")
    
    def finish(self) -> None:
        """Log final progress."""
        elapsed = time.time() - self.start_time
        rate = self.current / elapsed if elapsed > 0 else 0
        
        logger.info(f"{self.description} completed: {self.current} items in {format_duration(elapsed)} ({rate:.1f}/s)")
