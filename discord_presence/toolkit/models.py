"""
Data Models for Discord Presence Toolkit

This module contains data classes and models used throughout the toolkit
for representing Discord entities like members, presences, and activities.
"""

import time
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union


@dataclass
class DiscordActivity:
    """
    Represents a Discord activity/presence.
    
    Attributes:
        name: Activity name (e.g., "Visual Studio Code")
        activity_id: Unique activity identifier
        activity_type: Type of activity (game, stream, music, etc.)
        start_time: Unix timestamp when activity started
        end_time: Unix timestamp when activity ends (if applicable)
        platform: Platform the activity is running on
        state: Current state/status text
        details: Additional details about the activity
    """
    name: Optional[str] = None
    activity_id: Optional[str] = None
    activity_type: Optional[str] = None
    start_time: Optional[int] = None
    end_time: Optional[int] = None
    platform: Optional[str] = None
    state: Optional[str] = None
    details: Optional[str] = None
    
    @classmethod
    def from_discord_data(cls, data: Dict[str, Any], platform_main: str = 'unknown') -> 'DiscordActivity':
        """
        Create DiscordActivity from Discord API data.
        
        Args:
            data: Raw activity data from Discord
            platform_main: Main platform if not specified in activity
            
        Returns:
            DiscordActivity instance
        """
        # Activity type mapping
        activity_map = {
            0: 'game', 
            1: 'stream', 
            2: 'music', 
            3: 'watching', 
            4: 'custom'
        }
        
        timestamps = data.get('timestamps', {})
        
        return cls(
            name=data.get('name'),
            activity_id=data.get('id'),
            activity_type=activity_map.get(data.get('type'), f"type {data.get('type')}"),
            start_time=timestamps.get('start'),
            end_time=timestamps.get('end'),
            platform=data.get('platform', platform_main),
            state=data.get('state'),
            details=data.get('details')
        )
    
    def to_csv_row(self) -> List[Any]:
        """
        Convert to CSV row format.
        
        Returns:
            List of values for CSV output
        """
        return [
            self.name,
            self.activity_id,
            self.activity_type,
            self.start_time,
            self.end_time,
            self.platform,
            self.state,
            self.details
        ]


@dataclass
class DiscordMember:
    """
    Represents a Discord guild member.
    
    Attributes:
        user_id: Discord user ID
        guild_id: Guild this member belongs to
        is_bot: Whether the user is a bot
        main_status: Primary status (online, idle, dnd, offline)
        platform_main: Main platform the user is active on
        activities: List of current activities
        presence_data: Raw presence data from Discord
        member_data: Raw member data from Discord
    """
    user_id: str
    guild_id: str
    is_bot: bool = False
    main_status: str = 'offline'
    platform_main: str = 'unknown'
    activities: List[DiscordActivity] = field(default_factory=list)
    presence_data: Optional[Dict[str, Any]] = None
    member_data: Optional[Dict[str, Any]] = None
    
    @classmethod
    def from_discord_data(
        cls, 
        user_id: str, 
        guild_id: str, 
        member_data: Dict[str, Any]
    ) -> 'DiscordMember':
        """
        Create DiscordMember from Discord API data.
        
        Args:
            user_id: Discord user ID
            guild_id: Guild ID
            member_data: Raw member data from Discord
            
        Returns:
            DiscordMember instance
        """
        presence_data = member_data.get('presence', {})
        user_data = member_data.get('user', {})
        
        # Extract basic info
        is_bot = user_data.get('bot', False)
        main_status = presence_data.get('status', 'offline')
        
        # Determine main platform
        client_status = presence_data.get('client_status', {})
        platform_main = 'unknown'
        if client_status:
            # Find platform with the main status
            for platform, status in client_status.items():
                if status == main_status:
                    platform_main = platform
                    break
            if platform_main == 'unknown':
                platform_main = next(iter(client_status.keys()), 'unknown')
        
        # Parse activities
        activities = []
        for activity_data in presence_data.get('activities', []):
            activity = DiscordActivity.from_discord_data(activity_data, platform_main)
            activities.append(activity)
        
        return cls(
            user_id=user_id,
            guild_id=guild_id,
            is_bot=is_bot,
            main_status=main_status,
            platform_main=platform_main,
            activities=activities,
            presence_data=presence_data,
            member_data=member_data
        )
    
    def to_csv_rows(self, timestamp: float) -> List[List[Any]]:
        """
        Convert to CSV rows format.
        
        Args:
            timestamp: Unix timestamp for this data
            
        Returns:
            List of CSV rows (one per activity, or one with no activity)
        """
        base_row = [
            timestamp,
            self.guild_id,
            self.user_id,
            self.is_bot,
            self.main_status,
            self.platform_main
        ]
        
        if not self.activities:
            # No activities, return single row with empty activity fields
            return [base_row + [None] * 8]
        
        # Return one row per activity
        rows = []
        for activity in self.activities:
            rows.append(base_row + activity.to_csv_row())
        
        return rows


@dataclass
class GuildInfo:
    """
    Represents Discord guild information.
    
    Attributes:
        guild_id: Guild ID
        name: Guild name
        member_count: Number of members
        channels: List of accessible channel IDs
        properties: Raw guild properties from Discord
    """
    guild_id: str
    name: str
    member_count: int = 0
    channels: List[str] = field(default_factory=list)
    properties: Optional[Dict[str, Any]] = None
    
    @classmethod
    def from_discord_data(cls, guild_data: Dict[str, Any]) -> 'GuildInfo':
        """
        Create GuildInfo from Discord API data.
        
        Args:
            guild_data: Raw guild data from Discord
            
        Returns:
            GuildInfo instance
        """
        properties = guild_data.get('properties', {})
        
        return cls(
            guild_id=properties.get('id', guild_data.get('id')),
            name=properties.get('name', guild_data.get('name', 'Unknown')),
            member_count=properties.get('approximate_member_count', 0),
            properties=properties
        )


@dataclass
class ScrapingProgress:
    """
    Tracks scraping progress for a guild.
    
    Attributes:
        guild_id: Guild being scraped
        total_members_scraped: Total members processed
        ranges_completed: Number of member ranges completed
        channels_processed: Number of channels processed
        start_time: When scraping started
        last_update: Last update timestamp
    """
    guild_id: str
    total_members_scraped: int = 0
    ranges_completed: int = 0
    channels_processed: int = 0
    start_time: float = field(default_factory=time.time)
    last_update: float = field(default_factory=time.time)
    
    def update_progress(self, members_added: int = 0, ranges_added: int = 0, channels_added: int = 0):
        """
        Update progress counters.
        
        Args:
            members_added: Number of new members processed
            ranges_added: Number of new ranges completed
            channels_added: Number of new channels processed
        """
        self.total_members_scraped += members_added
        self.ranges_completed += ranges_added
        self.channels_processed += channels_added
        self.last_update = time.time()
    
    @property
    def elapsed_time(self) -> float:
        """Get elapsed time since scraping started."""
        return time.time() - self.start_time
    
    @property
    def members_per_second(self) -> float:
        """Get average members processed per second."""
        elapsed = self.elapsed_time
        return self.total_members_scraped / elapsed if elapsed > 0 else 0.0
