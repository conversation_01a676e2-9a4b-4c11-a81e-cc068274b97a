"""
Configuration Management for Discord Presence Toolkit

This module handles configuration loading, validation, and default values
for the Discord presence scraping toolkit.
"""

import os
import logging
from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Optional, Any


logger = logging.getLogger(__name__)


@dataclass
class ScrapingConfig:
    """
    Configuration for scraping behavior.
    
    Attributes:
        per_guild_limit: Maximum members to scrape per guild
        scraped_channels: Number of channels to scrape per guild
        max_ranges: Maximum member ranges to request at once
        range_size: Size of each member range (usually 100)
        request_delay: Delay between requests in seconds
        enable_presence_updates: Whether to listen for real-time presence updates
        enable_typing_events: Whether to listen for typing events
        enable_thread_events: Whether to listen for thread events
    """
    per_guild_limit: int = 50000
    scraped_channels: int = 1
    max_ranges: int = 3
    range_size: int = 100
    request_delay: float = 2.0
    enable_presence_updates: bool = True
    enable_typing_events: bool = False
    enable_thread_events: bool = False


@dataclass
class ConnectionConfig:
    """
    Configuration for connection behavior.
    
    Attributes:
        max_reconnect_attempts: Maximum reconnection attempts
        reconnect_delay: Base delay between reconnection attempts
        heartbeat_timeout_multiplier: Multiplier for heartbeat timeout
        enable_compression: Whether to enable zlib compression
        debug_logging: Whether to enable debug logging to file
        connection_timeout: Timeout for initial connection
    """
    max_reconnect_attempts: int = 5
    reconnect_delay: float = 5.0
    heartbeat_timeout_multiplier: float = 2.0
    enable_compression: bool = True
    debug_logging: bool = False
    connection_timeout: float = 30.0


@dataclass
class OutputConfig:
    """
    Configuration for output and logging.
    
    Attributes:
        log_path: Path for raw JSON logs
        output_path: Path for CSV output
        enable_csv_output: Whether to write CSV output
        csv_buffer_size: Number of rows to buffer before writing
        log_level: Logging level
        log_format: Log message format
        enable_progress_logging: Whether to log scraping progress
    """
    log_path: str = './raw_logs.txt'
    output_path: str = './output.csv'
    enable_csv_output: bool = True
    csv_buffer_size: int = 1000
    log_level: str = 'INFO'
    log_format: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    enable_progress_logging: bool = True


@dataclass
class DiscordConfig:
    """
    Configuration for Discord API interaction.
    
    Attributes:
        token: Discord authentication token
        client_type: Client type for build number ('stable', 'ptb', 'canary')
        user_agent: User agent string for HTTP requests
        capabilities: Client capabilities bitmask
        client_build_number: Discord client build number
        gateway_version: Gateway API version
        api_version: HTTP API version
    """
    token: str = ''
    client_type: str = 'stable'
    user_agent: str = (
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
        '(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    )
    capabilities: int = 1734653
    client_build_number: int = 448893
    gateway_version: int = 9
    api_version: int = 9


@dataclass
class ToolkitConfig:
    """
    Main configuration container for the Discord Presence Toolkit.
    
    Attributes:
        discord: Discord API configuration
        scraping: Scraping behavior configuration
        connection: Connection behavior configuration
        output: Output and logging configuration
    """
    discord: DiscordConfig = field(default_factory=DiscordConfig)
    scraping: ScrapingConfig = field(default_factory=ScrapingConfig)
    connection: ConnectionConfig = field(default_factory=ConnectionConfig)
    output: OutputConfig = field(default_factory=OutputConfig)
    
    @classmethod
    def from_environment(cls) -> 'ToolkitConfig':
        """
        Create configuration from environment variables.
        
        Returns:
            ToolkitConfig instance with values from environment
        """
        config = cls()
        
        # Discord configuration
        config.discord.token = os.getenv('DISCORD_TOKEN', '')
        config.discord.client_type = os.getenv('DISCORD_CLIENT_TYPE', 'stable')
        
        # Scraping configuration
        config.scraping.per_guild_limit = int(os.getenv('PER_GUILD_LIMIT', '50000'))
        config.scraping.scraped_channels = int(os.getenv('SCRAPED_CHANNELS', '1'))
        config.scraping.max_ranges = int(os.getenv('MAX_RANGES', '3'))
        config.scraping.request_delay = float(os.getenv('REQUEST_DELAY', '2.0'))
        config.scraping.enable_presence_updates = _parse_bool(os.getenv('ENABLE_PRESENCE_UPDATES', 'true'))
        config.scraping.enable_typing_events = _parse_bool(os.getenv('ENABLE_TYPING_EVENTS', 'false'))
        config.scraping.enable_thread_events = _parse_bool(os.getenv('ENABLE_THREAD_EVENTS', 'false'))
        
        # Connection configuration
        config.connection.max_reconnect_attempts = int(os.getenv('MAX_RECONNECT_ATTEMPTS', '5'))
        config.connection.reconnect_delay = float(os.getenv('RECONNECT_DELAY', '5.0'))
        config.connection.enable_compression = _parse_bool(os.getenv('ENABLE_COMPRESSION', 'true'))
        config.connection.debug_logging = _parse_bool(os.getenv('DEBUG_LOGGING', 'false'))
        config.connection.connection_timeout = float(os.getenv('CONNECTION_TIMEOUT', '30.0'))
        
        # Output configuration
        config.output.log_path = os.getenv('LOG_PATH', './raw_logs.txt')
        config.output.output_path = os.getenv('OUTPUT_PATH', './output.csv')
        config.output.enable_csv_output = _parse_bool(os.getenv('ENABLE_CSV_OUTPUT', 'true'))
        config.output.csv_buffer_size = int(os.getenv('CSV_BUFFER_SIZE', '1000'))
        config.output.log_level = os.getenv('LOG_LEVEL', 'INFO')
        config.output.enable_progress_logging = _parse_bool(os.getenv('ENABLE_PROGRESS_LOGGING', 'true'))
        
        return config
    
    def validate(self) -> None:
        """
        Validate configuration values.
        
        Raises:
            ValueError: If configuration is invalid
        """
        if not self.discord.token:
            raise ValueError("Discord token is required")
        
        if self.scraping.per_guild_limit <= 0:
            raise ValueError("per_guild_limit must be positive")
        
        if self.scraping.scraped_channels <= 0:
            raise ValueError("scraped_channels must be positive")
        
        if self.scraping.max_ranges <= 0 or self.scraping.max_ranges > 10:
            raise ValueError("max_ranges must be between 1 and 10")
        
        if self.connection.max_reconnect_attempts <= 0:
            raise ValueError("max_reconnect_attempts must be positive")
        
        if self.connection.reconnect_delay < 0:
            raise ValueError("reconnect_delay cannot be negative")
        
        # Ensure output directories exist
        for path_attr in ['log_path', 'output_path']:
            path = Path(getattr(self.output, path_attr))
            path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.debug("Configuration validation passed")
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary.
        
        Returns:
            Dictionary representation of configuration
        """
        return {
            'discord': {
                'token': '***' if self.discord.token else '',  # Hide token
                'client_type': self.discord.client_type,
                'capabilities': self.discord.capabilities,
                'client_build_number': self.discord.client_build_number,
                'gateway_version': self.discord.gateway_version,
                'api_version': self.discord.api_version,
            },
            'scraping': {
                'per_guild_limit': self.scraping.per_guild_limit,
                'scraped_channels': self.scraping.scraped_channels,
                'max_ranges': self.scraping.max_ranges,
                'range_size': self.scraping.range_size,
                'request_delay': self.scraping.request_delay,
                'enable_presence_updates': self.scraping.enable_presence_updates,
                'enable_typing_events': self.scraping.enable_typing_events,
                'enable_thread_events': self.scraping.enable_thread_events,
            },
            'connection': {
                'max_reconnect_attempts': self.connection.max_reconnect_attempts,
                'reconnect_delay': self.connection.reconnect_delay,
                'heartbeat_timeout_multiplier': self.connection.heartbeat_timeout_multiplier,
                'enable_compression': self.connection.enable_compression,
                'debug_logging': self.connection.debug_logging,
                'connection_timeout': self.connection.connection_timeout,
            },
            'output': {
                'log_path': self.output.log_path,
                'output_path': self.output.output_path,
                'enable_csv_output': self.output.enable_csv_output,
                'csv_buffer_size': self.output.csv_buffer_size,
                'log_level': self.output.log_level,
                'log_format': self.output.log_format,
                'enable_progress_logging': self.output.enable_progress_logging,
            }
        }


def _parse_bool(value: str) -> bool:
    """
    Parse boolean value from string.
    
    Args:
        value: String value to parse
        
    Returns:
        Boolean value
    """
    return value.lower() in ('true', '1', 'yes', 'on', 'enabled')


# CSV Headers for output
CSV_HEADERS = [
    'Unix_Timestamp',
    'Guild_ID', 
    'Member_ID',
    'Is_Bot',
    'Main_Status',
    'Platform_Main',
    'Act_Name',
    'Act_ID',
    'Act_Type',
    'Time_Start',
    'Time_End',
    'Platform',
    'State',
    'Details'
]
