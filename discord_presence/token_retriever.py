import sys
print(sys.path)

from camoufox.async_api import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from playwright.async_api import WebSocket, WebSocket<PERSON><PERSON><PERSON>, Playwright, async_playwright
import asyncio
from typing import Union
from aiohttp import ClientSession
import json

class PlaywrightLoginCollection():
    def log_requests(self, request):
        if request.method != 'POST' or not request.post_data:
            return False
        if 'login' in request.post_data:
            print(f"URL: {request.url}, Data: {request.post_data}")
            return True

    async def run(self, username:str='testusr', password:str='testpass'):
        async with AsyncCamoufox(headless=True) as browser:
            page = await browser.new_page()
            await page.goto('https://discord.com/login')
            
            login_html = await page.content()
            build_no = int(login_html.split('BUILD_NUMBER":"')[1].split('"')[0])
            print(f"Build Number: {build_no}")
            await asyncio.sleep(3)
            
            await page.locator('input:near(:text("Email or Phone Number"))').fill(username)
            await page.locator('input:near(:text("Forgot your password?"))').fill(password)
            await asyncio.sleep(1)
            
            await page.get_by_role('button').filter(has_text='Log In').click()
            async with page.expect_event('request', self.log_requests) as event:
                resp = await page.wait_for_event('response', lambda response: True)
                data = await resp.json()
                print(f"token: {data.get('token')}")
                print(f"user_id: {data.get('user_id')}")

            await asyncio.Future()

if __name__ == '__main__':
    scr = PlaywrightLoginCollection()
    asyncio.run(scr.run(username='<EMAIL>', password='irritablenightwraith'))