#need to import discord.py-self: https://github.com/dolfies/discord.py-self
from discord import Client, HTTPException, CustomActivity, \
ActivityType, Activity, Game, Streaming, Spotify, Status
import csv
import time
import asyncio
import random

class ActivityScraper(Client):

    def __init__(self, csvfile:str):
        super().__init__()
        self.filepath = csvfile
        return None

    async def _get_data(self):
        results = []
        for guild in self.guilds:
            
            print(f"Getting members for guild: {guild.name}")
            try:
                time_start = time.time()
                members = await guild.fetch_members(channels=random.sample(guild.channels, 5),cache=True, force_scraping=True, delay=0.3)
                collection_time = time.time() - time_start
                print(f"Time taken: {round(collection_time, 2)} seconds")
                for member in members:
                    if member.bot:
                        continue
                    if len(member.activities) == 0:
                        continue
                    status = str(member.status)

                    results.append((guild.id, guild.name, collection_time, member.id, status, member.activities))
            except Exception as e:
                results.append((guild.id, guild.name, "Fail: {e}", None, ['N/A']))
                continue
        return results

    async def _check_account(self):
        if self.required_action:
            # do a thing here to potentially fix required actions based on type
            return self.required_action

        if self.is_ws_ratelimited():
            #work out specifics of what this means and how to handle it
            return 'RATE_LIMITED'
        
        #if not self.tutorial.suppressed:
        #    try:
        #        self.tutorial.supress()
        #    except HTTPException:
        #        return 'TUTORIAL_SUPRESSION_FAIL'
            
        return None
    
    def _process_activity(self, activity):
        try:
            if isinstance(activity, Activity):
                appl = activity.application_id
                if appl == 1:
                    return 0
                url = activity.url
                act_name = activity.name
                plat = None
                start = activity.start
                end = activity.end
                session = activity.session_id
                type = activity.type
                if type == ActivityType.playing:
                    act_type = 'game'
                elif type == ActivityType.streaming:
                    act_type = 'stream'
                elif type == ActivityType.listening:
                    act_type = 'music'
                else:
                    act_type = 'other'

            elif isinstance(activity, Game):
                act_type = 'game'
                act_name = activity.name
                plat = None
                start = activity.start
                end = activity.end
                url = ''
                session = None
                appl = None

            elif isinstance(activity, Streaming):
                act_type = 'stream'
                act_name = activity.game
                plat = activity.platform
                url = activity.url
                session = None
                start = None
                end = None
                appl = None

            elif isinstance(activity, Spotify):
                act_type = 'music'
                start = activity.start
                end = activity.end
                dat = activity.to_dict()
                session = dat['session_id']
                act_name = dat['details']
                url = f"https://open.spotify.com/track/{dat['sync_id']}"
                plat = 'Spotify'
                appl = None

            elif isinstance(activity, CustomActivity):
                act_type='other'
                end=activity.expires_at
                start=None
                session=None
                act_name=activity.emoji
                url=None
                plat=None
                appl=None
            
            else:
                return 0

            out_row = (act_type, appl, act_name, plat, url, session, start, end)

            return out_row
        
        except Exception as e:
            print(f'ERROR {e}')
            return 0
       

    def _download_and_process_data(self, data):
        writer = csv.writer(open(self.filepath, 'w'))
        writer.writerow(('guild_id', 'guild_name', 'collection_duration', 'user_id', 'status', \
                        'type', 'application_id', 'title', 'platform',  'url', \
                        'session_id', 'start', 'end'))
        for row in data:
            guild_row = row[:-1]
            activities = row[-1]

            for activity in activities:
                act_row = self._process_activity(activity)
                if isinstance(act_row, int):
                    continue
                out_row = (*guild_row, *act_row)
                writer.writerow(out_row)

    async def on_ready(self):
        duration_init = time.time()
        print(f"Logged in as {self.user}")
        account_issues = await self._check_account()
        if account_issues is not None:
            print(account_issues)
            raise Exception(f"Account issue: {account_issues}")
        data = await self._get_data()
        self._download_and_process_data(data)
        print(f"Total time: {round((time.time()-duration_init)/60, 2)} minutes")
        await self.close()
        self.clear()

    
    async def on_error(self, event_method, /, *args, **kwargs):
        return await super().on_error(event_method, *args, **kwargs)

if __name__ == '__main__':
    scraper = ActivityScraper('discordActivity-25_09_08-v14.csv')
    scraper.run('MTM5OTQxMTI3NjA4OTY1NTMwNg.Gb9Shm.grrCOKzeNUybpaK0OghTMCq7CDjOAtAT-CFobA')