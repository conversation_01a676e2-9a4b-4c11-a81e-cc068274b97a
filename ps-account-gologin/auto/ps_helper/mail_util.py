from auto.ps_helper.mail_config import *
import requests
from requests.auth import HTT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from human_id import generate_id
import imaplib
import email as pymail


def get_users():
    """
    Returns dictionary containing all currently registered users.
    """
    r = requests.get(
        f"{MAIL_ADMIN_URL}/users",
        params={"format": "json"},
        auth=HTTPBasicAuth(ADMIN_EMAIL, ADMIN_PASSWORD),
    )
    return r.json()


def generate_random_email(word_count=3):
    """
    Generates a random email address of the form one.two.three@mail_domain.com.
    Uses https://github.com/orf/human_id with 3 words by default (2.7E7 possible email addresses).
    """
    return f"{generate_id(word_count=word_count, separator='.')}@{MAIL_DOMAIN}"


def add_user(email=None, password=None):
    """
    Adds a new user email account to the domain, returns created email.
    By default, this function generates a random email address with the default user password.
    To override, optionally supply the `email` and/or `password` kwargs.
    """
    if email is None:
        email = generate_random_email()
    if password is None:
        password = USER_PASSWORD
    r = requests.post(
        f"{MAIL_ADMIN_URL}/users/add",
        data={"email": email, "password": password},
        auth=HTTPBasicAuth(ADMIN_EMAIL, ADMIN_PASSWORD),
    )
    print(r)
    return email


def get_user_inbox(email, password=USER_PASSWORD, search_criterion="ALL"):
    """
    Returns array containing inbox for a given email in form
    [
            {
                    'from': $SENDER,
                    'subject': $EMAIL_SUBJECT,
                    'content': $EMAIL_BODY
            },
            ...
    ]
    To restrict results for a given criterion, pass the kwarg search_criterion, e.g.
            search_criterion='(FROM "<EMAIL>")'
    """
    inbox = []
    mail = imaplib.IMAP4_SSL(MAIL_URL)
    mail.login(email, password)
    mail.select("inbox")
    mail_ids = []
    status, data = mail.search(None, search_criterion)
    for block in data:
        mail_ids.extend(block.split())
    for id in mail_ids:
        status, data = mail.fetch(id, "(RFC822)")
        for response_part in data:
            if isinstance(response_part, tuple):
                message = pymail.message_from_bytes(response_part[1])
                mail_from = message["from"]
                mail_subject = message["subject"]
                received = message["Date"]
                received_datetime = pymail.utils.parsedate_to_datetime(received)

                if message.is_multipart():
                    mail_content = ""
                    for part in message.get_payload():
                        if part.get_content_type() == "text/plain":
                            payload = part.get_payload(decode=True)
                            charset = part.get_content_charset()
                            if charset:
                                payload = payload.decode(charset)
                            mail_content += payload
                else:
                    payload = message.get_payload(decode=True)
                    charset = message.get_content_charset()
                    if charset:
                        mail_content = payload.decode(charset)

                inbox.append(
                    {
                        "from": mail_from,
                        "subject": mail_subject,
                        "content": mail_content,
                        "received_datetime": received_datetime,
                    }
                )
    return inbox


def get_user_spam(email, password=USER_PASSWORD, search_criterion="ALL"):
    """
    Returns array containing inbox for a given email in form
    [
            {
                    'from': $SENDER,
                    'subject': $EMAIL_SUBJECT,
                    'content': $EMAIL_BODY
            },
            ...
    ]
    To restrict results for a given criterion, pass the kwarg search_criterion, e.g.
            search_criterion='(FROM "<EMAIL>")'
    """
    inbox = []
    mail = imaplib.IMAP4_SSL(MAIL_URL)
    mail.login(email, password)
    mail.select("Spam")
    mail_ids = []
    status, data = mail.search(None, search_criterion)
    for block in data:
        mail_ids.extend(block.split())
    for id in mail_ids:
        status, data = mail.fetch(id, "(RFC822)")
        for response_part in data:
            if isinstance(response_part, tuple):
                message = pymail.message_from_bytes(response_part[1])
                mail_from = message["from"]
                mail_subject = message["subject"]
                received = message["Date"]
                received_datetime = pymail.utils.parsedate_to_datetime(received)

                if message.is_multipart():
                    mail_content = ""
                    for part in message.get_payload():
                        if part.get_content_type() == "text/plain":
                            payload = part.get_payload(decode=True)
                            charset = part.get_content_charset()
                            if charset:
                                payload = payload.decode(charset)
                            mail_content += payload
                else:
                    mail_content = message.get_payload(decode=True)
                    charset = message.get_content_charset()
                    if charset:
                        mail_content = payload.decode(charset)

                inbox.append(
                    {
                        "from": mail_from,
                        "subject": mail_subject,
                        "content": mail_content,
                        "received_datetime": received_datetime,
                    }
                )
    return inbox
