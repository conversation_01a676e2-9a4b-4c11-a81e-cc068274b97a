from postgres import PostgrestClient
import asyncio
from antidetect.antidetect_browser import Antidetect
from antidetect.fingerprints import CustomViewport
import random

proxy_list = [
    "202.182.74.26",
    "104.171.167.35",
    "109.121.25.24",
    "109.121.27.11",
    "12.5.178.47",
    "204.242.178.17",
    "50.231.154.16",
    "109.121.24.21",
    "12.5.179.32",
    "121.91.225.40",
    "50.223.132.50",
    "104.171.175.149",
    "109.121.12.29",
    "88.209.255.14",
    "118.188.158.47",
    "89.187.20.228",
    "65.215.43.150",
    "88.216.20.27",
    "109.233.184.10",
    "158.62.214.31",
    "206.206.92.2",
    "102.129.247.58",
    "88.216.185.151",
    "216.185.53.52",
    "65.215.31.155",
    "88.216.185.94",
    "178.219.14.83",
    "************",
    "*************",
    "**************",
    "************",
    "*************",
    "************",
    "*************",
    "************",
    "*************",
    "*************",
    "*************",
    "***********",
    "**************",
    "**************",
    "*************",
    "*************",
    "*************",
    "*************",
    "*************",
    "************",
    "**************",
    "*************",
    "************",
]

async def start_browser():
    host = random.choice(proxy_list)
    proxy = {
        'server': f'{host}:4444',
        'username': "b03f165be1",
        'password': "w1q4cGzT"
    }
    antidetect = await Antidetect().start()
    browser = await antidetect.new_browser(proxy_string=f"http://{proxy["username"]}:{proxy["password"]}@{proxy['server']}", headless=False)
    page = await browser.new_page()
    await page.set_viewport_size({"width": 1080, "height": 720})
    await page.goto("https://wikipedia.com")
    while True:
        await asyncio.sleep(1)
    return browser

async def run():
    await start_browser()

if __name__ == "__main__":
    asyncio.run(run())