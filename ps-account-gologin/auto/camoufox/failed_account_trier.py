import os
import json
import time
from enum import Enum
from pathlib import Path
from typing import Dict, Optional, Any
from datetime import datetime
import sys
import asyncio

import pytz
import query_builder as qb
from camoufox import Camoufox, launch_options
from auto.camoufox.mail import get_email_verification_code
from auto.camoufox.proxy_stats_manager import update_proxy_stats
from auto.camoufox.proxy_manager import get_proxy
from auto.camoufox.account_query_manager import update_sso_code, number_of_daily_updated_accounts

ps_public = qb.Schema('public', db_name='ps')

def opts_path(name: str) -> str:
    return f"auto/camoufox/profiles/{name}/opts.json"


def config_exists(name: str) -> bool:
    return Path(opts_path(name)).exists()


def get_profile(name: str, proxy: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """Get or create a profile configuration.

    Args:
        name: Profile name
        proxy: Optional proxy configuration

    Returns:
        Profile configuration dictionary
    """
    if config_exists(name=name):
        with open(opts_path(name)) as opts_file:
            return json.load(opts_file)
    return generate_profile(name, proxy)

def save_profile(name:str, opts):
    profile_dir = os.path.dirname(opts_path(name))
    try:
        os.makedirs(profile_dir, exist_ok=True)
        with open(opts_path(name), "w") as opts_file:
            json.dump(opts, opts_file, indent=4)
        return opts

    except Exception as e:
        error_message = str(e).lower()
        print(f"Error saving profile: {e}")
        raise

def generate_profile(name: str, proxy: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """Generate a new profile configuration.

    Args:
        name: Profile name
        proxy: Optional proxy configuration

    Returns:
        New profile configuration dictionary
    """
    profile_dir = os.path.dirname(opts_path(name))

    try:
        opts = launch_options(
            user_data_dir=profile_dir,
            os=['windows'],
            proxy=proxy,
            geoip=True,
            window=(1080, 720),
            humanize=True,
            headless=True
        )

        if proxy:
            update_proxy_stats(proxy, True)

        os.makedirs(profile_dir, exist_ok=True)
        with open(opts_path(name), "w") as opts_file:
            json.dump(opts, opts_file, indent=4)
        return opts

    except Exception as e:
        error_message = str(e).lower()
        print(f"Error generating profile: {e}")

        if proxy and any(term in error_message for term in [
            'proxy', 'connection', 'network', 'timeout', 'socket',
            'dns', 'connect', 'unreachable', 'refused', 'reset'
        ]):
            print(f"Proxy connection failed during profile generation with proxy: {proxy['server']}")
        update_proxy_stats(proxy, False)

        raise

def setup_profile(profile_name: str, proxy: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
    """
    Set up a profile for the PlayStation account.

    Args:
        profile_name: Name of the profile to set up
        proxy: Optional proxy configuration

    Returns:
        Tuple of (profile_opts, success):
            - profile_opts: The profile options if successful, None otherwise
    """
    # Check if profile exists, if not, get a new proxy
    if not config_exists(name=profile_name) and proxy is None:
        proxy = get_proxy()

    # Get or create profile
    try:
        profile_opts = get_profile(profile_name, proxy)
        return profile_opts
    except Exception as e:
        error_message = str(e).lower()
        print(f"Failed to get or create profile: {e}")

        if proxy and any(term in error_message for term in [
            'proxy', 'connection', 'network', 'timeout', 'socket',
            'dns', 'connect', 'unreachable', 'refused', 'reset'
        ]):
            print(f"Proxy connection failed during profile creation with proxy: {proxy['server']}")

        return None
    
# profile_opts['proxy'] = {'server': 'http://204.242.178.17:4444', 'username': 'b03f165be1', 'password': 'w1q4cGzT'}
    # new_proxy = profile_opts.get('proxy')
    # print(new_proxy)
    # save_profile(profile_name, profile_opts)

def get_email_and_password(email):
    result = qb.Query(ps_public.accounts_identity).where(
            qb.c_.email == email
        ).select(qb.c_.password).fetchone_sync()
    return result['password']

def update_sso_code(email: str) -> None:
        npsso = input("enter npsso: ")
        result = qb.Query(ps_public.accounts_identity).where(
            qb.c_.email == email
        ).select(qb.c_.id).fetchone_sync()

        if not result:
            raise Exception(f"Account not found with email {email} in accounts_identity table")

        account_id = result['id']
        now = datetime.now(tz=pytz.UTC)

        qb.APIQuery(ps_public.accounts_sso).update_sync(
            identifier={"account_id": account_id},
            value={"sso": npsso, "updated_at": now}
        )

        print(f"Updated SSO code: {npsso}")

def main():
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        asyncio.set_event_loop(asyncio.ProactorEventLoop())

    print("starting...")

    email = '<EMAIL>'
    profile_name = email.split('@')[0]

    profile_opts = setup_profile(profile_name)
    
    profile_opts['proxy'] = get_proxy()
    profile_opts['headless'] = True
    save_profile(profile_name, profile_opts)
    profile_opts['headless'] = False

    password = get_email_and_password(email)
    print(email)
    print(password)

    
    with Camoufox(from_options=profile_opts, persistent_context=True) as browser:
        page = browser.new_page()

        try:
            page.goto('https://playstation.com')
            while True:
                print("a: get code" \
                "b: npsso"
                "c: exit")
                a = input()
                if a == 'a':
                    code = get_email_verification_code(email)
                    print(code)
                elif a == 'b':
                    update_sso_code(email)
                    return
                else:
                    return
        except Exception as e:
            print(e)


if __name__ == '__main__':
    main()