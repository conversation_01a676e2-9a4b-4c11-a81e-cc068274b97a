"""
Script to refresh PlayStation accounts.
"""


from auto.camoufox.ps_browser import refresh_profile
from auto.camoufox.pull_ps_accounts import get_all_accounts
from auto.camoufox.failed_accounts_manager import add_failed_account
from auto.camoufox.account_query_manager import number_of_daily_updated_accounts


number_of_accounts_daily = 500
accounts_tried_untill_pause = 100

PAUSE_PER_BATCH = 300
PAUSE_AFTER_COMPLETE = 3600

def main():
    print("Starting PlayStation account refresh...")

    current_refreshed_accounts = number_of_daily_updated_accounts()
    print(f"Current refreshed accounts: {current_refreshed_accounts}")
    if current_refreshed_accounts >= number_of_accounts_daily:
        print(f"Daily limit reached. Sleeping for {PAUSE_AFTER_COMPLETE/60} minutes... Then Exiting.")
        return

    accounts = get_all_accounts()
    

    if len(accounts) < number_of_accounts_daily:
        print(f"Warning: Only {len(accounts)} accounts available after filtering out failed accounts")

    if not accounts:
        print("No accounts to refresh. Exiting.")
        return

    batch_count = 0
    successes = 0
    fails = 0

    for account in accounts:
        if successes > 0:
            print(f"{successes / (successes + fails) * 100:.2f} accounts refreshed successfully so far")
        else:
            print(f"0.00% accounts refreshed successfully so far")
        email = account.get('email', 'unknown')
        print(f"Refreshed {current_refreshed_accounts} of {number_of_accounts_daily}: starting {email}")

        try:
            # Make sure browser is properly closed between iterations
            success = refresh_profile(account)

            if success:
                current_refreshed_accounts += 1
                successes += 1
                print(f"✅ Account {email} refreshed successfully")
            else:
                add_failed_account(email)
                error_msg = getattr(refresh_profile, 'last_error', 'Unknown error')
                print(f"❌ Account {email} failed: {error_msg}")
                fails += 1
        except Exception as e:
            error_message = str(e)
            # Check if it's a proxy-related error
            if any(term in error_message.lower() for term in [
                'proxy', 'connection', 'network', 'timeout', 'socket',
                'dns', 'connect', 'unreachable', 'refused', 'reset'
            ]):
                print(f"⚠️ Account {email} encountered a proxy error - skipping without marking as failed")
            elif "asyncio" in error_message.lower() or "async api" in error_message.lower():
                print(f"⚠️ Account {email} failed due to asyncio conflict - restarting process")
                # Force restart the script to clear asyncio state
                import os
                import sys
                os.execv(sys.executable, ['python'] + sys.argv)
            else:
                print(f"⚠️ Account {email} failed with unexpected error: {error_message}")

        batch_count += 1
        print(f"{current_refreshed_accounts}/{number_of_accounts_daily} accounts refreshed successfully so far")
        print(f"{batch_count}/{accounts_tried_untill_pause} accounts tried in current batch")

        if current_refreshed_accounts == number_of_accounts_daily:
            print("Daily limit reached. Exiting.")
            break

        if batch_count == accounts_tried_untill_pause:
            batch_count = 0
            print(f"Pausing for {PAUSE_PER_BATCH/60} minutes...")
            from auto.camoufox.loading_sequence import random_display_sleep
            random_display_sleep(PAUSE_PER_BATCH)


if __name__ == "__main__":
    main()
