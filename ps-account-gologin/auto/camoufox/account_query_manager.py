import query_builder as qb
from datetime import timedelta, datetime
import pytz

ps_public = qb.Schema('public', db_name='ps')

def number_of_daily_updated_accounts() -> list[dict[str, str]]:
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    result = qb.Query(ps_public.accounts_sso).where(
            qb.c_.updated_at >= today
        ).select(qb.c_.account_id).fetchall_sync()
    return len(result)

def update_sso_code(npsso: str, email: str) -> None:
        """Update the SSO code in the database.

        Args:
            npsso: The SSO code to update
            email: email of the account to update
        """
        result = qb.Query(ps_public.accounts_identity).where(
            qb.c_.email == email
        ).select(qb.c_.id).fetchone_sync()

        if not result:
            raise Exception(f"Account not found with email {email} in accounts_identity table")

        account_id = result['id']
        now = datetime.now(tz=pytz.UTC)

        qb.APIQuery(ps_public.accounts_sso).update_sync(
            identifier={"account_id": account_id},
            value={"sso": npsso, "updated_at": now}
        )

        print(f"Updated SSO code: {npsso}")

if __name__ == "__main__":
    number_of_daily_updated_accounts()