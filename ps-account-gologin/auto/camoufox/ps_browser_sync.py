"""PlayStation account automation using CamouFox.

This module provides functionality to automate PlayStation account Login/Logout
using CamouFox.
"""

import os
import json
import time
from enum import Enum
from pathlib import Path
from typing import Dict, Optional, Any
from datetime import datetime

import pytz
import query_builder as qb
from camoufox import Camoufox, launch_options
from auto.camoufox.mail import get_email_verification_code
from auto.camoufox.proxy_stats_manager import update_proxy_stats
from auto.camoufox.proxy_manager import get_proxy
from auto.camoufox.account_query_manager import update_sso_code, number_of_daily_updated_accounts
ps_db = qb.db(db_name='ps')
playstation_profiles = qb.Schema('account_profiles', db_name='ps')

# Database configuration
ps_public = qb.Schema('public', db_name='ps')

# Selectors for PlayStation website elements
class Selectors:
    # Sign in/out buttons
    SIGN_IN_BUTTON = "button[data-qa='web-toolbar#signin-button']"
    SIGN_OUT_BUTTON = "button[data-qa='web-toolbar#profile-container#profile-dropdown#item-list#sign-out#button']"
    # Alternative sign out selectors to try
    SIGN_OUT_BUTTON_ALT1 = "button[data-qa*='sign-out']"
    SIGN_OUT_BUTTON_ALT2 = "a[data-qa*='sign-out']"
    SIGN_OUT_BUTTON_ALT3 = "[data-qa*='sign-out-button']"
    PROFILE_ICON = "button[data-qa='web-toolbar#profile-container#profile-icon#dropdown-toggler']"
    PROFILE_IMAGE = "span[data-qa='web-toolbar#profile-container#profile-icon#image']"
    PROFILE_IMAGE_IMG = "img[data-qa='web-toolbar#profile-container#profile-icon#image#image']"

    # Login inputs
    EMAIL_INPUT = "id=signin-entrance-input-signinId"
    EMAIL_NEXT_BUTTON = "button#signin-entrance-button[data-qa='button-primary']"
    PASSWORD_INPUT = "id=signin-password-input-password"
    PASSWORD_SUBMIT_BUTTON = "button[data-qa='button-primary'][type='submit']"

    # Verification
    VERIFICATION_INPUT = "input[data-qa='#input']"
    VERIFICATION_BUTTON = "button[data-qa='button-primary']"

    # Error messages
    ERROR_TEXTS = [
        "Algo ha salido mal",
        "Something has gone wrong",
        "Este dispositivo ha enviado demasiadas solicitudes al servidor. Inténtalo de nuevo más tarde"
    ]

    # Bot detection messages
    BOT_DETECTION_TEXTS = [
        "No se ha podido conectar con el servidor",
        "Can't connect to the server"
    ]

    # Error symbol that indicates bot detection
    ERROR_SYMBOL = "div.psw-icon-size-4.icon--HzoyX"

    # SSO cookie page
    SSO_CONTENT = "body > pre"

    # Accept cookies popup
    ACCEPT_COOKIES_BUTTON = "button#onetrust-accept-btn-handler"


class PageState(str, Enum):
    UNKNOWN = "unknown"
    ON_HOME_PAGE = "on_home_page"
    CAN_CLICK_SIGN_IN = "can_click_sign_in"
    IS_SIGNED_IN = "is_signed_in"
    CAN_CLICK_SIGN_OUT = "can_click_sign_out"
    CAN_ENTER_EMAIL = "can_enter_email"
    CAN_ENTER_PASSWORD = "can_enter_password"
    SOMETHING_WENT_WRONG_PAGE = "something went wrong"
    BOT_DETECTED = "bot detected"
    VERIFY_EMAIL = "verify email"


async def opts_path(name: str) -> str:
    return f"auto/camoufox/profiles/{name}/opts.json"


async def config_exists(name: str) -> bool:
    return Path(opts_path(name)).exists()


async def get_profile(name: str, proxy: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """Get or create a profile configuration.

    Args:
        name: Profile name
        proxy: Optional proxy configuration

    Returns:
        Profile configuration dictionary
    """
    if await config_exists(name=name):
        with open(opts_path(name)) as opts_file:
            return json.load(opts_file)
    return await generate_profile(name, proxy)

async def get_profile_from_db(profile_name, proxy: Optional[Dict[str, str]] = None):
    result = qb.Query(playstation_profiles.profiles).where(
        playstation_profiles.profiles.c.profile_name == profile_name
    ).fetchone_sync()
    
    if result:
        return result['profile_data']
    return await generate_profile(profile_name, proxy)


async def generate_profile(name: str, proxy: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """Generate a new profile configuration.

    Args:
        name: Profile name
        proxy: Optional proxy configuration

    Returns:
        New profile configuration dictionary
    """
    profile_dir = os.path.dirname(opts_path(name))

    try:
        opts = launch_options(
            user_data_dir=profile_dir,
            os=['windows'],
            proxy=proxy,
            geoip=True,
            window=(1080, 720),
            humanize=True,
            headless=True
        )

        if proxy:
            update_proxy_stats(proxy, True)

        os.makedirs(profile_dir, exist_ok=True)
        with open(opts_path(name), "w") as opts_file:
            json.dump(opts, opts_file, indent=4)
        return opts

    except Exception as e:
        error_message = str(e).lower()
        print(f"Error generating profile: {e}")

        if proxy and any(term in error_message for term in [
            'proxy', 'connection', 'network', 'timeout', 'socket',
            'dns', 'connect', 'unreachable', 'refused', 'reset'
        ]):
            print(f"Proxy connection failed during profile generation with proxy: {proxy['server']}")
        update_proxy_stats(proxy, False)

        raise


async def regenerate_profile_with_new_proxy(name: str, max_attempts: int = 3) -> Dict[str, Any]:
    """Regenerate a profile with a new proxy.

    This function gets a new proxy from the proxy manager and regenerates
    the profile configuration with it. It will try multiple proxies if needed.

    Args:
        name: Profile name
        max_attempts: Maximum number of proxy attempts

    Returns:
        Updated profile configuration dictionary
    """

    attempts = 0
    last_error = None

    while attempts < max_attempts:
        new_proxy = get_proxy()
        print(f"Regenerating profile with new proxy (attempt {attempts+1}/{max_attempts}): {new_proxy['server']}")

        # Remove the existing profile configuration
        profile_path = opts_path(name)
        if os.path.exists(profile_path):
            try:
                os.remove(profile_path)
                print(f"Removed existing profile configuration: {profile_path}")
            except Exception as e:
                print(f"Warning: Could not remove existing profile: {e}")

        # Try to generate a new profile with the new proxy
        try:
            return await generate_profile(name, new_proxy)
        except Exception as e:
            last_error = e
            error_message = str(e).lower()
            print(f"Failed to generate profile with proxy {new_proxy['server']}: {e}")

            # Check if it's a proxy-related error
            if any(term in error_message for term in [
                'proxy', 'connection', 'network', 'timeout', 'socket',
                'dns', 'connect', 'unreachable', 'refused', 'reset'
            ]):
                print(f"Proxy connection issue detected. Will try another proxy.")
                attempts += 1
            else:
                # If it's not a proxy-related error, re-raise
                raise

    # If we've tried max_attempts proxies and all failed, raise the last error
    print(f"Failed to generate profile after {max_attempts} proxy attempts")
    if last_error:
        raise last_error
    else:
        raise Exception(f"Failed to generate profile after {max_attempts} proxy attempts")

class PageStateFinder:
    """Class to determine the current state of the PlayStation website."""

    async def __init__(self, page) -> None:
        """Initialize the PageStateFinder.

        Args:
            page: The CamouFox page object
        """
        self._page = page
        self.page_state = PageState.UNKNOWN

    async def check_home_page(self) -> PageState:
        """Check the state of the PlayStation homepage.

        Returns:
            The detected page state
        """
        # Check for sign in button on home page
        if self._page.locator(Selectors.SIGN_IN_BUTTON).first.is_visible():
            self.page_state = PageState.CAN_CLICK_SIGN_IN
            return self.page_state
        # Check for sign out button in dropdown
        if self._page.locator(Selectors.SIGN_OUT_BUTTON).first.is_visible():
            self.page_state = PageState.CAN_CLICK_SIGN_OUT
            return self.page_state
        # Check for profile icon or image (user is signed in)
        if self._page.locator(Selectors.PROFILE_ICON).first.is_visible() or \
           self._page.locator(Selectors.PROFILE_IMAGE).first.is_visible() or \
           self._page.locator(Selectors.PROFILE_IMAGE_IMG).first.is_visible():
            self.page_state = PageState.IS_SIGNED_IN
            return self.page_state
        self.page_state = PageState.ON_HOME_PAGE
        return self.page_state

    async def get_state(self) -> PageState:
        """Determine the current state of the page.

        Returns:
            The detected page state
        """
        # Check for accept cookies popup first (highest priority)
        if self._page.locator(Selectors.ACCEPT_COOKIES_BUTTON).is_visible():
            print("Found accept cookies popup")
            self.page_state = PageState.ON_HOME_PAGE  # Treat as home page action
            return self.page_state

        # Check for error pages
        for error_text in Selectors.ERROR_TEXTS:
            if self._page.get_by_text(error_text).is_visible():
                print("Something went wrong. Refreshing page")
                self.page_state = PageState.SOMETHING_WENT_WRONG_PAGE
                return self.page_state

        # Check for bot detection
        for bot_text in Selectors.BOT_DETECTION_TEXTS:
            if self._page.get_by_text(bot_text).is_visible():
                print("Detected as bot, abort program")
                self.page_state = PageState.BOT_DETECTED
                return self.page_state

        # Check for error symbol that indicates bot detection
        if self._page.locator(Selectors.ERROR_SYMBOL).is_visible():
            error_element = self._page.locator(Selectors.ERROR_SYMBOL).first
            aria_label = "unknown"
            try:
                aria_label = error_element.get_attribute("aria-label") or "unknown"
            except:
                pass
            print(f"Error symbol detected (aria-label: {aria_label}) - treating as bot detection")
            self.page_state = PageState.BOT_DETECTED
            return self.page_state

        # Check for PlayStation homepage
        if self._page.url.startswith('https://www.playstation.com'):
            time.sleep(5)
            await self.check_home_page()
            return self.page_state

        # Check for login flow pages
        # Check for next button (email page)
        if self._page.locator(Selectors.EMAIL_NEXT_BUTTON).first.is_visible():
            self.page_state = PageState.CAN_ENTER_EMAIL
            return self.page_state

        # Check for sign in button (password page)
        if self._page.locator(Selectors.PASSWORD_SUBMIT_BUTTON).first.is_visible():
            self.page_state = PageState.CAN_ENTER_PASSWORD
            return self.page_state

        # Check for verify button (verification page)
        if self._page.locator(Selectors.VERIFICATION_BUTTON).first.is_visible() and \
           self._page.locator(Selectors.VERIFICATION_INPUT).first.is_visible():
            self.page_state = PageState.VERIFY_EMAIL
            return self.page_state

        self.page_state = PageState.UNKNOWN
        return self.page_state

class Controller:
    """Controller class for interacting with the PlayStation website."""

    async def __init__(self, page, data: Dict[str, str]) -> None:
        """Initialize the Controller.

        Args:
            page: The CamouFox page object
            data: Dictionary containing account data (email, password)
        """
        self._page = page
        self._data = data

    async def click_sign_in(self) -> None:
        """Click the sign in button on the homepage."""
        print("Clicking sign in")

        # First check if there's a cookies popup to handle
        if await self.click_accept_cookies():
            time.sleep(1)  # Brief pause after handling cookies

        button = self._page.locator(Selectors.SIGN_IN_BUTTON).first
        button.click()
        time.sleep(2)

    async def debug_buttons(self):
        buttons = self._page.locator("button[data-qa]").all()
        print(f"Found {len(buttons)} buttons with data-qa attributes:")
        for i, button in enumerate(buttons[:10]):  # Limit to first 10
            try:
                qa_attr = button.get_attribute("data-qa")
                text = button.text_content()
                visible = button.is_visible()
                print(f"  {i+1}. data-qa='{qa_attr}', text='{text}', visible={visible}")
            except:
                pass
        a = input()

    async def click_first_sign_out(self) -> None:
        """Click the profile icon to open the dropdown menu."""
        print("Clicking profile")

        # First check if there's a cookies popup to handle
        cookies_handled = await self.click_accept_cookies()
        if cookies_handled:
            print("Cookie popup handled, proceeding with sign out")
            time.sleep(1)  # Brief pause after handling cookies

        # self.debug_buttons()

        # Try to find and click the profile icon using different selectors
        if self._page.locator(Selectors.PROFILE_ICON).first.is_visible():
            self._page.locator(Selectors.PROFILE_ICON).first.click()
            print("clicked 1")
        elif self._page.locator(Selectors.PROFILE_IMAGE).first.is_visible():
            print("clicked 2")
            self._page.locator(Selectors.PROFILE_IMAGE).first.click()
        elif self._page.locator(Selectors.PROFILE_IMAGE_IMG).first.is_visible():
            print("clicked 3")
            self._page.locator(Selectors.PROFILE_IMAGE_IMG).first.click()
        else:
            print("Warning: Could not find profile icon or image")
            return

        time.sleep(2)
        """Click the sign out button in the dropdown menu."""
        print("Clicking sign out")

        
        """Click the sign out button in the dropdown menu."""
        print("Clicking sign out")

        # First check if there's a cookies popup to handle
        cookies_handled = await self.click_accept_cookies()
        if cookies_handled:
            print("Cookie popup handled, proceeding with sign out button click")
            time.sleep(1)

        # Try multiple selectors for the sign out button
        selectors_to_try = [
            Selectors.SIGN_OUT_BUTTON,
            Selectors.SIGN_OUT_BUTTON_ALT1,
            Selectors.SIGN_OUT_BUTTON_ALT2,
            Selectors.SIGN_OUT_BUTTON_ALT3
        ]

        # self.debug_buttons()
        
        clicked = False
        for selector in selectors_to_try:
            try:
                button = self._page.locator(selector).first
                if button.is_visible():
                    button.click()
                    print(f"Successfully clicked sign out button with selector: {selector}")
                    clicked = True
                    break
            except Exception as e:
                print(f"Failed to click with selector {selector}: {e}")
                continue
        
        if not clicked:
            print("Warning: Could not find any sign out button")
            
        time.sleep(2)


    async def click_second_sign_out(self) -> None:
        """Click the sign out button in the dropdown menu."""
        print("Clicking sign out")

        # First check if there's a cookies popup to handle
        cookies_handled = await self.click_accept_cookies()
        if cookies_handled:
            print("Cookie popup handled, proceeding with sign out button click")
            time.sleep(1)

        # Try multiple selectors for the sign out button
        selectors_to_try = [
            Selectors.SIGN_OUT_BUTTON,
            Selectors.SIGN_OUT_BUTTON_ALT1,
            Selectors.SIGN_OUT_BUTTON_ALT2,
            Selectors.SIGN_OUT_BUTTON_ALT3
        ]
        
        clicked = False
        for selector in selectors_to_try:
            try:
                button = self._page.locator(selector).first
                if button.is_visible():
                    button.click()
                    print(f"Successfully clicked sign out button with selector: {selector}")
                    clicked = True
                    break
            except Exception as e:
                print(f"Failed to click with selector {selector}: {e}")
                continue
        
        if not clicked:
            print("Warning: Could not find any sign out button")
            
        time.sleep(2)

    async def click_verify_email(self) -> None:
        """Handle email verification process.

        Gets verification code from email and enters it into the verification field.
        """
        print("Getting verification code")
        code = get_email_verification_code(self._data['email'])
        print(f"Code is: {code}")

        locator = self._page.locator(Selectors.VERIFICATION_INPUT).first

        # Type the code
        locator.click()
        locator.type(code)
        entered = locator.input_value()

        # Retry if code wasn't entered correctly
        if entered != code:
            locator = self._page.locator(Selectors.VERIFICATION_INPUT).first
            # Clear the field and try again
            locator.click()
            locator.press("Control+a")
            locator.press("Backspace")
            time.sleep(0.3)
            locator.type(code)
            entered = locator.input_value()

        print("Clicking verify")
        button = self._page.locator(Selectors.VERIFICATION_BUTTON).first
        button.click()
        time.sleep(2)

    async def autofill_email(self) -> None:
        """Enter email address and click next."""
        locator = self._page.locator(Selectors.EMAIL_INPUT).first
        print("Filling email input")

        # Type the email character by character
        locator.click()
        locator.type(self._data['email'])

        # Wait for the button to become enabled
        print("Waiting for button to be enabled")
        self._page.wait_for_selector(f"{Selectors.EMAIL_NEXT_BUTTON}:not([disabled])")

        # Click the button
        print("Clicking next button")
        button = self._page.locator(Selectors.EMAIL_NEXT_BUTTON).first
        button.click()
        time.sleep(2)

    async def autofill_password(self) -> None:
        """Enter password and click sign in."""
        locator = self._page.locator(Selectors.PASSWORD_INPUT).first
        print("Filling password input")

        # Type the password character by character
        locator.click()
        locator.type(self._data['password'])

        # Click the sign in button
        print("Clicking sign in button")

        # self.debug_buttons()

        button = self._page.locator(Selectors.PASSWORD_SUBMIT_BUTTON).first
        button.click()

        time.sleep(2)

    async def get_sso(self) -> None:
        """Get and update the SSO code."""
        print("Navigating to SSO cookie page")
        self._page.goto('https://ca.account.sony.com/api/v1/ssocookie')
        text = self._page.locator(Selectors.SSO_CONTENT).text_content()
        npsso = json.loads(text)['npsso']
        print(f"SSO code: {npsso}")
        if npsso:
            update_sso_code(npsso, self._data['email'])

    async def refresh_page(self) -> None:
        """Reload the current page."""
        print("Reloading page")
        self._page.reload()

    async def wait_on_home_page(self) -> None:
        """Wait on the home page for a short time."""
        print("Waiting on home page")

        # First check if there's a cookies popup to handle
        if await self.click_accept_cookies():
            return

        time.sleep(5)

    async def click_accept_cookies(self) -> bool:
        """Click the accept cookies button if present.

        Returns:
            bool: True if cookies button was found and clicked, False otherwise
        """
        print("Checking for accept cookies button")
        try:
            button = self._page.locator(Selectors.ACCEPT_COOKIES_BUTTON).first
            if button.is_visible():
                print("Clicking accept cookies button")
                button.click()
                time.sleep(2)
                return True
        except Exception as e:
            print(f"Error clicking accept cookies button: {e}")
        return False

async def attempt_to_connect(page, proxy: Optional[Dict[str, str]] = None, max_attempts: int = 20) -> tuple[bool, bool]:
    """Attempt to connect to the PlayStation website.

    Args:
        page: The CamouFox page object
        proxy: The proxy being used (for logging purposes)
        max_attempts: Maximum number of connection attempts

    Returns:
        Tuple of (success, proxy_issue):
            - success: True if connection was successful, False otherwise
            - proxy_issue: True if the error was related to proxy, False otherwise
    """
    count = 0
    proxy_issue = False

    while count < max_attempts:
        try:
            page.goto('https://playstation.com')
            return True, False  # Success, no proxy issue
        except Exception as e:
            error_message = str(e).lower()
            print(f"Connection error: {e}")

            # Check if it's a proxy-related error
            if any(term in error_message for term in [
                'proxy', 'connection', 'network', 'timeout', 'socket',
                'dns', 'connect', 'unreachable', 'refused', 'reset'
            ]):
                if proxy:
                    print(f"Detected proxy connection issue with proxy: {proxy['server']}")
                else:
                    print("Detected proxy connection issue")
                proxy_issue = True

            count += 1
            print(f"Attempt {count}/{max_attempts} to load")

            # If we've tried a few times and it's a proxy issue, give up earlier
            if proxy_issue and count >= 3:
                if proxy:
                    print(f"Multiple proxy connection failures detected with proxy: {proxy['server']}, giving up early")
                else:
                    print("Multiple proxy connection failures detected, giving up early")
                break

    return False, proxy_issue  # Failed to connect, return proxy issue status

async def setup_profile(profile_name: str, proxy: Optional[Dict[str, str]] = None, retry_with_new_proxy: bool = True) -> tuple[Optional[Dict[str, Any]], bool]:
    """
    Set up a profile for the PlayStation account.

    Args:
        profile_name: Name of the profile to set up
        proxy: Optional proxy configuration
        retry_with_new_proxy: Whether to retry with a new proxy if connection fails

    Returns:
        Tuple of (profile_opts, success):
            - profile_opts: The profile options if successful, None otherwise
            - success: True if successful, False otherwise
    """
    # Check if profile exists, if not, get a new proxy
    if not await config_exists(name=profile_name) and proxy is None:
        proxy = get_proxy()

    # Get or create profile
    try:
        profile_opts = get_profile(profile_name, proxy)
        # profile_opts = get_profile_from_db(profile_name, proxy)
        return profile_opts, True
    except Exception as e:
        error_message = str(e).lower()
        print(f"Failed to get or create profile: {e}")

        # Check if it's a proxy-related error
        if proxy and any(term in error_message for term in [
            'proxy', 'connection', 'network', 'timeout', 'socket',
            'dns', 'connect', 'unreachable', 'refused', 'reset'
        ]):
            print(f"Proxy connection failed during profile creation with proxy: {proxy['server']}")

            if retry_with_new_proxy:
                print("Will retry with a new proxy.")
                # Regenerate the profile with a new proxy
                try:
                    new_profile_opts = regenerate_profile_with_new_proxy(profile_name)
                    return new_profile_opts, True
                except Exception as inner_e:
                    print(f"Failed to regenerate profile with new proxy: {inner_e}")
                    return None, False

        return None, False

async def update_profile_proxy(profile_name: str) -> Optional[Dict[str, Any]]:
    """
    Update the proxy in an existing profile.
    
    Args:
        profile_name: Name of the profile to update
        
    Returns:
        Updated profile options or None if failed
    """
    try:
        if config_exists(name=profile_name):
            with open(opts_path(profile_name)) as opts_file:
                profile_opts = json.load(opts_file)
                
            # Get a new proxy
            new_proxy = get_proxy()
            print(f"Updating profile with new proxy: {new_proxy['server']}")
            profile_opts['proxy'] = new_proxy
            
            # Save the updated profile
            profile_dir = os.path.dirname(opts_path(profile_name))
            os.makedirs(profile_dir, exist_ok=True)
            with open(opts_path(profile_name), "w") as opts_file:
                json.dump(profile_opts, opts_file, indent=4)
            
            # Update proxy stats
            update_proxy_stats(new_proxy, True)
            
            return profile_opts
        else:
            # If profile doesn't exist, create a new one
            new_proxy = get_proxy()
            return generate_profile(profile_name, new_proxy)
    except Exception as e:
        print(f"Failed to update profile with new proxy: {e}")
        return None

async def handle_proxy_retry(profile_name: str, account_data: Dict[str, str]) -> bool:
    """
    Handle retrying with a new proxy.

    Args:
        profile_name: Name of the profile to regenerate
        account_data: Dictionary containing account details

    Returns:
        bool: True if successful, False otherwise
    """
    print("Proxy connection issue detected. Will retry with a new proxy.")
    
    # Instead of recursively calling refresh_profile, we'll just return False
    # and let the main function handle the retry
    try:
        # Update the profile with a new proxy
        updated_profile = update_profile_proxy(profile_name)
        if updated_profile:
            print("Profile updated with new proxy. Please retry the account.")
            return False
        else:
            print("Failed to update profile with new proxy.")
            return False
    except Exception as e:
        print(f"Failed to update profile with new proxy: {e}")
        return False

async def handle_connection(page, current_proxy: Optional[Dict[str, str]], profile_name: str, account_data: Dict[str, str], retry_with_new_proxy: bool) -> bool:
    """
    Handle connection to the PlayStation website.

    Args:
        page: The CamouFox page object
        current_proxy: The current proxy configuration
        profile_name: Name of the profile
        account_data: Dictionary containing account details
        retry_with_new_proxy: Whether to retry with a new proxy if connection fails

    Returns:
        bool: True if connection successful, False otherwise
    """
    # Connect to PlayStation website
    connection_success, proxy_issue = attempt_to_connect(page, current_proxy)

    if not connection_success:
        print("Failed to connect to PlayStation website after multiple attempts")

        # If it's a proxy issue and we should retry with a new proxy
        if proxy_issue and retry_with_new_proxy:
            return handle_proxy_retry(profile_name, account_data)

        return False

    return True


async def execute_state_machine(page, account_data: Dict[str, str]) -> bool:
    """
    Execute the state machine for account refresh.

    Args:
        page: The CamouFox page object
        account_data: Dictionary containing account details

    Returns:
        bool: True if successful, False otherwise
    """
    # Initialize state finder and controller
    psf = PageStateFinder(page)
    controller = Controller(page, account_data)

    # Map states to actions
    state_action_map = {
        PageState.ON_HOME_PAGE: controller.wait_on_home_page,
        PageState.CAN_CLICK_SIGN_IN: controller.click_sign_in,
        PageState.IS_SIGNED_IN: controller.click_first_sign_out,
        PageState.CAN_CLICK_SIGN_OUT: controller.click_second_sign_out,
        PageState.CAN_ENTER_EMAIL: controller.autofill_email,
        PageState.CAN_ENTER_PASSWORD: controller.autofill_password,
        PageState.SOMETHING_WENT_WRONG_PAGE: controller.refresh_page,
        PageState.VERIFY_EMAIL: controller.click_verify_email,
    }

    # Define valid state transitions
    state_transition_map = {
        PageState.ON_HOME_PAGE: [PageState.CAN_CLICK_SIGN_IN, PageState.IS_SIGNED_IN, PageState.ON_HOME_PAGE, PageState.SOMETHING_WENT_WRONG_PAGE],
        PageState.CAN_CLICK_SIGN_IN: [PageState.IS_SIGNED_IN, PageState.CAN_ENTER_EMAIL, PageState.SOMETHING_WENT_WRONG_PAGE, PageState.CAN_CLICK_SIGN_IN],
        PageState.IS_SIGNED_IN: [PageState.CAN_CLICK_SIGN_OUT, PageState.SOMETHING_WENT_WRONG_PAGE, PageState.IS_SIGNED_IN, PageState.CAN_CLICK_SIGN_IN],
        PageState.CAN_CLICK_SIGN_OUT: [PageState.CAN_CLICK_SIGN_IN, PageState.SOMETHING_WENT_WRONG_PAGE, PageState.CAN_CLICK_SIGN_OUT],
        PageState.CAN_ENTER_EMAIL: [PageState.CAN_ENTER_PASSWORD, PageState.SOMETHING_WENT_WRONG_PAGE],
        PageState.CAN_ENTER_PASSWORD: [PageState.IS_SIGNED_IN, PageState.ON_HOME_PAGE, PageState.SOMETHING_WENT_WRONG_PAGE, PageState.VERIFY_EMAIL],
        PageState.VERIFY_EMAIL: [PageState.ON_HOME_PAGE, PageState.CAN_CLICK_SIGN_IN, PageState.IS_SIGNED_IN, PageState.SOMETHING_WENT_WRONG_PAGE],
        PageState.SOMETHING_WENT_WRONG_PAGE: [PageState.ON_HOME_PAGE, PageState.CAN_CLICK_SIGN_IN, PageState.CAN_ENTER_EMAIL, PageState.SOMETHING_WENT_WRONG_PAGE],
        PageState.UNKNOWN: [PageState.ON_HOME_PAGE, PageState.CAN_CLICK_SIGN_IN, PageState.IS_SIGNED_IN, PageState.CAN_ENTER_EMAIL, PageState.CAN_ENTER_PASSWORD,
                            PageState.SOMETHING_WENT_WRONG_PAGE, PageState.VERIFY_EMAIL],
    }

    # Initialize state machine
    bot_detected = False
    success = False
    current_state = psf.get_state()
    initial_state = current_state  # Track the initial state
    attempted_signout = False  # Track if we've attempted sign out from initial IS_SIGNED_IN state
    completed_signout = False  # Track if we've successfully signed out
    print(f"\n=== INITIAL STATE: {current_state} ===")

    # Main state machine loop
    while current_state != PageState.UNKNOWN and not success and not bot_detected:
        # Special handling for initial IS_SIGNED_IN state
        if current_state == PageState.IS_SIGNED_IN and initial_state == PageState.IS_SIGNED_IN and not attempted_signout:
            print("Initial state is IS_SIGNED_IN, attempting sign out")
            attempted_signout = True

        # Track when we've successfully signed out (reached a non-signed-in state)
        if initial_state == PageState.IS_SIGNED_IN and attempted_signout and current_state != PageState.IS_SIGNED_IN:
            if not completed_signout:
                print("Successfully signed out, now proceeding with sign in process")
                completed_signout = True

        success = execute_action_for_state(current_state, state_action_map, page)
        if not success:
            return False

        # Find next state
        success, bot_detected, current_state, found_next_state = find_next_state(
            psf, current_state, state_transition_map, page, initial_state, attempted_signout, completed_signout
        )

        # Check if we're stuck
        if not found_next_state and not success:
            print("No valid transitions found. Exiting.")
            break

        # Get SSO code if signed in
        if current_state == PageState.IS_SIGNED_IN:
            # Get SSO if we weren't initially signed in, OR if we were initially signed in but completed the signout process
            if initial_state != PageState.IS_SIGNED_IN or (initial_state == PageState.IS_SIGNED_IN and completed_signout):
                controller.get_sso()

    return success


async def execute_action_for_state(current_state: PageState, state_action_map: Dict[PageState, callable], page) -> bool:
    """
    Execute the action for the current state.

    Args:
        current_state: The current state
        state_action_map: Map of states to actions
        page: The CamouFox page object

    Returns:
        bool: True if action executed successfully, False otherwise
    """
    retry_attempt = 0
    max_retries = 1  # Try once more if it fails
    last_error = None

    action = state_action_map.get(current_state)
    print(f"\n=== EXECUTING ACTION FOR STATE: {current_state} ===")

    while retry_attempt <= max_retries:
        try:
            action()
            time.sleep(0.5)
            return True  # If successful, return True
        except Exception as e:
            error_message = str(e)
            last_error = e
            if "Timeout" in error_message and "exceeded" in error_message:
                retry_attempt += 1
                if retry_attempt <= max_retries:
                    print(f"Timeout error occurred during {current_state} action. Retry attempt {retry_attempt}/{max_retries}...")
                    # Try to go back to the homepage to recover
                    try:
                        page.goto('https://playstation.com')
                        time.sleep(3)
                    except:
                        pass
                else:
                    print(f"Timeout error persisted after {max_retries} retry attempts. Moving on...")
                    # Store the last error for reference
                    execute_action_for_state.last_error = "Timeout error"
                    # Return False to indicate failure
                    return False
            else:
                # Store the last error for reference
                execute_action_for_state.last_error = error_message
                # For other errors, re-raise
                raise

    return True


async def find_next_state(psf: PageStateFinder, current_state: PageState,
                   state_transition_map: Dict[PageState, list], page, initial_state: PageState,
                   attempted_signout: bool = False, completed_signout: bool = False) -> tuple[bool, bool, PageState, bool]:
    """
    Find the next state in the state machine.

    Args:
        psf: The PageStateFinder instance
        current_state: The current state
        state_transition_map: Map of states to possible next states
        page: The CamouFox page object
        initial_state: The initial state when the state machine started
        attempted_signout: Whether we've attempted sign out from initial IS_SIGNED_IN state
        completed_signout: Whether we've successfully completed the sign out process

    Returns:
        Tuple of (success, bot_detected, current_state, found_next_state):
            - success: True if a successful state was found
            - bot_detected: True if bot detection was triggered
            - current_state: The updated current state
            - found_next_state: True if a valid next state was found
    """
    possible_next_states = state_transition_map.get(current_state, [])
    found_next_state = False
    attempts = 0
    max_attempts = 10
    success = False
    bot_detected = False

    # Try to find a valid next state
    while attempts < max_attempts and not found_next_state:
        next_state = psf.get_state()
        print(f"\n=== DETECTED STATE: {next_state} ===")

        # Check for special states
        if next_state == PageState.BOT_DETECTED:
            print("Bot detected - aborting")
            bot_detected = True
            break

        # Treat IS_SIGNED_IN as success if:
        # 1. We weren't initially signed in, OR
        # 2. We were initially signed in but have completed the signout process
        if next_state == PageState.IS_SIGNED_IN and (initial_state != PageState.IS_SIGNED_IN or completed_signout):
            print("Successfully signed in")
            current_state = next_state
            success = True
            break
        elif next_state == PageState.IS_SIGNED_IN and initial_state == PageState.IS_SIGNED_IN and attempted_signout and not completed_signout:
            print("Still signed in after attempting sign out - this may indicate the profile icon wasn't clicked properly")
            # Force a page refresh and try to find the sign out button
            page.goto('https://playstation.com')
            time.sleep(3)
            # Continue with the state machine instead of treating as success
        elif next_state == PageState.IS_SIGNED_IN and initial_state == PageState.IS_SIGNED_IN and not completed_signout:
            print("Still signed in (initial state was signed in, continuing sign-out process)")
            # Continue with the state machine instead of treating as success

        # Check if the next state is valid
        if next_state != PageState.UNKNOWN and next_state in possible_next_states:
            current_state = next_state
            found_next_state = True
        else:
            attempts += 1
            if attempts == max_attempts:
                # Last resort: go back to homepage
                page.goto('https://playstation.com')
            print(f"Attempt {attempts}/{max_attempts}: Failed to find next state, waiting 3 seconds")
            time.sleep(3)

    return success, bot_detected, current_state, found_next_state


async def refresh_profile(account_data: Dict[str, str], proxy: Optional[Dict[str, str]] = None, retry_with_new_proxy: bool = True) -> bool:
    """
    Refresh a PlayStation account profile using CamouFox.

    Args:
        account_data: Dictionary containing account details (email, password)
        proxy: Optional proxy configuration
        retry_with_new_proxy: Whether to retry with a new proxy if connection fails

    Returns:
        bool: True if successful, False otherwise
    """
    # Reset last error
    refresh_profile.last_error = None
    
    # Get profile name from email (username part)
    profile_name = account_data['email'].split('@')[0]

    # Set up the profile
    try:
        profile_opts, _ = setup_profile(profile_name, proxy, retry_with_new_proxy)
        if not profile_opts:
            refresh_profile.last_error = "Failed to set up profile"
            return False
    except Exception as e:
        refresh_profile.last_error = str(e)
        return False

    # Launch browser with the profile
    try:
        with Camoufox(from_options=profile_opts, persistent_context=True, headless=True) as browser:
            page = browser.new_page()

            # Get the current proxy from the profile options for logging purposes
            current_proxy = profile_opts.get('proxy')

            # Handle connection to PlayStation website
            if not handle_connection(page, current_proxy, profile_name, account_data, retry_with_new_proxy):
                # If connection failed and we need to retry with a new proxy,
                # update the profile with a new proxy for the next attempt
                if retry_with_new_proxy:
                    update_profile_proxy(profile_name)
                refresh_profile.last_error = "Failed to connect to PlayStation website"
                return False

            # Execute the state machine
            success = execute_state_machine(page, account_data)
            if not success and hasattr(execute_action_for_state, 'last_error'):
                refresh_profile.last_error = execute_action_for_state.last_error
            return success
    except Exception as e:
        refresh_profile.last_error = str(e)
        print(f"Error during browser session: {e}")
        return False

# Initialize the last_error attribute
refresh_profile.last_error = None
execute_action_for_state.last_error = None

if __name__ == "__main__":
    print("Starting PlayStation account refresh")

    # Account credentials
    account_data = {
        "email": "<EMAIL>",
        "password": "lastrowA1!",
    }

    # Run the refresh process without explicitly providing a proxy
    # The function will get a proxy if needed for a new profile
    success = refresh_profile(account_data)

    if success:
        print("Account refresh completed successfully")
    else:
        print("Account refresh failed")
