import asyncpg
import json
import os
from pathlib import Path
import query_builder as qb

ps_db = qb.db(db_name='ps')
playstation_profiles = qb.Schema('account_profiles', db_name='ps')

async def save_profile(profile_name, email, profile_data):
    info = {
        'profile_name': profile_name,
        'email': email,
        'profile_data': profile_data
    }
    insert_stmt = qb.insert(playstation_profiles.profiles).values(info)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_update(
            index_elements=[playstation_profiles.profiles.c.profile_name],
            set_={ 'email': insert_stmt.excluded.email, 'profile_data': insert_stmt.excluded.profile_data }
            )
    )
    await qb.engine(insert_stmt, db_name='ps').execute()

async def get_profile(email):
    result = await qb.Query(playstation_profiles.profiles).where(
        playstation_profiles.profiles.c.email == email
    ).fetchone()
    return result

async def migrate_profiles_to_db(profiles_dir):
    profiles_path = Path(profiles_dir)
    print(profiles_path)
    for profile_folder in profiles_path.iterdir():
        if profile_folder.is_dir():
            opts_file = profile_folder / "opts.json"
            if opts_file.exists():
                profile_name = profile_folder.name
                
                # Extract email from profile data if available
                with open(opts_file, 'r') as f:
                    profile_data = json.load(f)
                    
                # Try to determine email from profile name or data
                email = f"{profile_name}@ampgams.com"
                if 'email' in profile_data:
                    email = profile_data['email']
                
                await save_profile(profile_name, email, profile_data)
                print(f"Migrated profile: {profile_name}")

async def main():
    data = await get_profile("<EMAIL>")
    print(data)

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())