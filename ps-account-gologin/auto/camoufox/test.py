import sys
import math
import time
from datetime import datetime, timedelta


def main():
    # Configuration
    total_seconds = 18  # 30 minutes
    interval = 0.1
    width = 60
    lines = 3

    # Characters for the wave animation
    wave_chars = ["▁", "▂", "▃", "▄", "▅", "▆", "▇", "█", "▇", "▆", "▅", "▄", "▃", "▂"]

    # Colors
    RESET = '\033[0m'
    BLUE = '\033[38;5;39m'
    CYAN = '\033[38;5;51m'
    TEAL = '\033[38;5;37m'
    GREEN = '\033[38;5;35m'
    COLORS = [BLUE, CYAN, TEAL, GREEN]

    # Progress bar settings
    progress_width = 40

    # Calculate steps
    steps = int(total_seconds / interval)

    # Print initial message and create space for animation
    print(f"Pausing for 30 minutes... Starting at {datetime.now().strftime('%H:%M:%S')}")
    end_time = datetime.now() + timedelta(seconds=total_seconds)
    print(f"Will resume at {end_time.strftime('%H:%M:%S')}\n")

    for _ in range(lines + 2):  # +2 for progress bar and time
        print()

    for i in range(steps):
        # Calculate progress
        progress = min(i / steps, 1.0)
        elapsed = i * interval
        remaining = total_seconds - elapsed

        # Create frames for each line
        frames = []

        # Generate wave patterns for each line with different phases
        for line in range(lines):
            frame = []
            phase_offset = line * 0.7  # Different phase for each line
            speed_factor = 0.15 - (line * 0.03)  # Different speed for each line

            for x in range(width):
                # Calculate wave position with smoother sine wave
                wave_pos = math.sin((i * speed_factor) + (x * 0.2) + phase_offset)
                # Map to character index
                char_idx = int((wave_pos + 1) * (len(wave_chars) - 1) / 2)
                # Add colored character
                color = COLORS[line % len(COLORS)]
                frame.append(f"{color}{wave_chars[char_idx]}{RESET}")

            frames.append(''.join(frame))

        # Create progress bar
        filled_width = int(progress_width * progress)
        progress_bar = f"[{CYAN}{'█' * filled_width}{RESET}{'░' * (progress_width - filled_width)}]"

        # Format time display
        mins, secs = divmod(int(remaining), 60)
        hours, mins = divmod(mins, 60)
        time_display = f"{hours:02d}:{mins:02d}:{secs:02d} remaining"
        percentage = f"{progress * 100:.1f}%"

        # Move cursor up to overwrite previous frame
        sys.stdout.write('\033[F' * (lines + 2))

        # Print each line of the animation
        for frame in frames:
            print(frame)

        # Print progress bar and time
        print(f"{progress_bar} {percentage} ({time_display})")
        print(f"Elapsed: {int(elapsed)}s / {total_seconds}s")

        # Sleep for the interval
        time.sleep(interval)

if __name__ == "__main__":
    main()