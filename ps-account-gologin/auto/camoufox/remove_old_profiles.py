# checks for profiles in auto/camoufox/profiles that are not in the database from the past month and deletes them

import os
import query_builder as qb
from datetime import datetime, timedelta
import shutil
import stat

def handle_remove_readonly(func, path, exc_info):
    os.chmod(path, stat.S_IWRITE)
    func(path)

playstation_public = qb.Schema('public', db_name='ps')

refreshed_ids = qb.Query(playstation_public.accounts_sso).where(
        qb.c_.updated_at > datetime.now() - timedelta(days=30)
    ).select(qb.c_.account_id).fetchall_sync()

refreshed_ids = [r['account_id'] for r in refreshed_ids]

refreshed_emails = qb.Query(playstation_public.accounts_identity).where(
        qb.c_.id.in_(refreshed_ids)
    ).select(qb.c_.email).fetchall_sync()

refreshed_emails = [r['email'] for r in refreshed_emails]
profile_names = [e.split('@')[0] for e in refreshed_emails]

deleted_count = 0
kept_count = 0
total_count = 0
for folder in os.listdir('auto/camoufox/profiles'):
    total_count += 1
    if folder not in profile_names:
        print(f"Deleting {folder}")
        try:
            shutil.rmtree(f"auto/camoufox/profiles/{folder}", onerror=handle_remove_readonly)
            print(f"Successfully deleted {folder}")
            deleted_count += 1
        except Exception as e:
            print(f"Error deleting {folder}: {e}")
    else:
        print(f"Keeping {folder}")
        kept_count += 1

print(f"Deleted {deleted_count} profiles, kept {kept_count} profiles, total {total_count} profiles")
        
