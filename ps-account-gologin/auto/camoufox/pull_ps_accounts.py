"""
Module to pull PlayStation accounts from the database.
"""

import query_builder as qb
from typing import List, Dict
from auto.camoufox.failed_accounts_manager import filter_out_failed_accounts
from datetime import datetime, timedelta

ps_public = qb.Schema('public', db_name='ps')

def get_number_of_accounts(number_of_accounts=10) -> List[Dict[str, str]]:
    """
    Get PlayStation accounts from the database, filtering out previously failed accounts.

    Args:
        number_of_accounts: Maximum number of accounts to return

    Returns:
        List of account dictionaries with 'email' and 'password' keys
    """
    filtered_accounts = get_all_accounts()
    return filtered_accounts[:number_of_accounts]

def time_ago(n=2, untit='weeks'):
    return datetime.now() - timedelta(**{untit: n})


def get_all_accounts() -> List[Dict[str, str]]:
    """
    Get PlayStation accounts from the database, filtering out previously failed accounts.

    Args:
        number_of_accounts: Maximum number of accounts to return

    Returns:
        List of account dictionaries with 'email' and 'password' keys
    """

    all_account_details_query = qb.Query(
        qb.select(qb.c_.email, qb.c_.password)
        .select_from(ps_public.accounts_sso)
        .join(ps_public.accounts_identity, qb.c_.account_id == qb.c_.id)
        # .where(qb.c_.account_id > 1400)
        .where(qb.c_.updated_at > time_ago(4, 'weeks'))
        .where(qb.c_.updated_at < time_ago(2, 'days'))
        .order_by(qb.c_.updated_at.asc())
    )

    all_account_details = all_account_details_query.fetchall_sync()

    # Filter out previously failed accounts
    filtered_accounts = filter_out_failed_accounts(all_account_details)
    print(len(filtered_accounts))

    # Truncate to the requested number
    return filtered_accounts

if __name__ == "__main__":
    print(time_ago(7, 'days'))
