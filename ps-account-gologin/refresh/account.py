import query_builder as qb
from datetime import datetime, timedelta
from pydantic import BaseModel
from datetime import datetime
from pathlib import Path
import os 

PROFILE_DIR = './auto/camoufox/profiles'

ps_public = qb.db('ps').public

class Account(BaseModel):
	id: int
	firstname: str
	lastname: str
	dob: datetime
	city: str
	province: str
	postcode: str
	email: str
	password: str
	username: str
	
	@property
	def context_dir(self)->str:
		profile_name = self.email.split('@')[0]
		return f'{PROFILE_DIR}/{profile_name}'


async def get_accounts(max_age_weeks: int, limit: int=None) -> list[Account]:
	"""Get account to refresh."""
	accounts_query = qb.Query(
		qb.select(*ps_public.accounts_identity.c)
		.select_from(ps_public.accounts_sso)
		.join(ps_public.accounts_identity, qb.c_.account_id == qb.c_.id)
		.where(qb.c_.updated_at > datetime.now() - timedelta(weeks=max_age_weeks))
		.where(qb.c_.updated_at < datetime.now() - timedelta(days=2))
		.order_by(qb.c_.updated_at.asc())
		.limit(limit)
	)
	accounts_result = await accounts_query.fetchall()
	accounts = []
	for r in accounts_result:
		account = Account(**r)
		if not Path(account.context_dir).exists():
			os.makedirs(account.context_dir)	
		accounts.append(account)
	return accounts 