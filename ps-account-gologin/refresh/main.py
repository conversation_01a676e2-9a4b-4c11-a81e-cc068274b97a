from refresh.browser import start_browser
from refresh.proxy import get_proxy
from refresh.account import get_accounts
from refresh.logging import logger
import asyncio
import pprint



async def main():
	logger.info("Starting auto PlayStation refresh")
	accounts = await get_accounts(max_age_weeks=2)
	for account in accounts:
		logger.info(f"Begin refresh for:\n{pprint.pformat(account.model_dump())}")
		try:
			async with start_browser(account.context_dir, proxy=get_proxy(skip_rotate=False)) as browser:
				while True:
					await asyncio.sleep(10)
		except KeyboardInterrupt:
			exit(0)
		except Exception as e:
			logger.info(f"Skipped due to error: {e}")


if __name__ == '__main__':
	asyncio.run(main())