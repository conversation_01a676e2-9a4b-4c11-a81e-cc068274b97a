from camoufox.async_api import As<PERSON><PERSON><PERSON><PERSON><PERSON>
from playwright.async_api import Page, BrowserContext
import json
from pathlib import Path
import asyncio
from contextlib import asynccontextmanager, AsyncExitStack

def load_base_opts(context_dir: str):
	"""Load the opts.json file from the persistent context dir"""
	path = Path(context_dir)
	if not path.exists():
		raise Exception(f"User context dir: {context_dir} not found")
	with open(path.joinpath('opts.json'), 'r') as infile:
		opts = json.loads(infile.read())
	return opts

def load_opts(context_dir: str, proxy: dict[str, str] = None):
	"""Load the opts.json for the context, overriding some options"""
	opts = load_base_opts(context_dir)
	opts['proxy'] = proxy
	opts['user_data_dir']=context_dir
	opts['geoip']=True,


async def handle_route(route, request):
	"""Block gmedia.playstation.com to save residential proxy bandwidth"""
	if 'gmedia.playstation.com' in request.url:
		await route.abort()
	else:
		await route.continue_()


async def clear_storage(page: Page, context: BrowserContext):
    # 1. Clear cookies and cache
    await context.clear_cookies()
    await context.clear_permissions()
    await context.storage_state(path=None)  # reset storage state

    # 2. Clear localStorage, sessionStorage, and IndexedDB
    await page.evaluate("""() => {
        // Clear localStorage and sessionStorage
        localStorage.clear();
        sessionStorage.clear();
        
        // Clear IndexedDB
        return new Promise((resolve, reject) => {
            if (!window.indexedDB) {
                resolve();
                return;
            }
            const req = indexedDB.databases ? indexedDB.databases() : Promise.resolve([]);
            Promise.resolve(req).then(dbs => {
                if (!dbs) {
                    resolve();
                    return;
                }
                let done = 0;
                if (dbs.length === 0) resolve();
                dbs.forEach(dbInfo => {
                    const name = dbInfo.name;
                    if (!name) return;
                    const deleteReq = indexedDB.deleteDatabase(name);
                    deleteReq.onsuccess = deleteReq.onerror = deleteReq.onblocked = () => {
                        done++;
                        if (done === dbs.length) resolve();
                    };
                });
            });
        });
    }""")

@asynccontextmanager
async def start_browser(context_dir: str, proxy: dict[str, str]):
	try:
		opts = load_opts(context_dir, proxy=proxy)
	except:
		opts = None
	async with AsyncExitStack() as stack:
		browser = await stack.enter_async_context(AsyncCamoufox(
			from_options=opts,
			user_data_dir = context_dir,
			persistent_context=True,
			geoip=True,
			headless=False,
			proxy=proxy
		))
		await browser.route('**/*', handle_route)
		yield browser
	
