import requests
import random
import time
from refresh.logging import logger

def get_iproyal_residential():
	session = ''.join(chr(random.randint(1,30)) for _ in range(10))
	proxy = {
		"server": "geo.iproyal.com:12321",
		"username": "ampere1",
		"password": f"n6pNGfmC7fqKV5c_country-us_session-{session}_lifetime-10m"
	}
	return proxy 


def get_ip(proxy):
    import requests
    username = proxy.get('username', None)
    password = proxy.get('password', None)
    server = proxy['server']
    if username is not None:
        proxy = f"{username}:{password}@{server}"
    else:
        proxy = server
    resp = requests.get('http://ipecho.net/plain', 
                        proxies={'http': f'http://{proxy}', 'https': f'https://{proxy}'})
    return resp.text 

def rotate_mobile_proxy():
	logger.info('rotating mobile proxy')
	url = "https://apid.iproyal.com/v1/orders/57837756/rotate-ip/iRl5FeCmAk"
	resp = requests.get(url)
	waited = 0
	while  resp.status_code // 100 != 2:
		logger.info(f"rotating proxy failed: {resp.text}")
		time.sleep(30)
		resp = requests.get(url)
		waited += 1
		if waited > 20:
			logger.error("Issue with mobile proxy")
			exit(1)

def get_iproyal_mobile(skip_rotate: bool = False):
	if not skip_rotate:
		rotate_mobile_proxy()	
	proxy ={
		"server": "us3.4g.iproyal.com:7139",
		"username": "pvx8SHK",
		"password": "Ym9EE13ZwcCqET7"
	}
	logger.info(f"IP: {get_ip(proxy)}")
	return proxy



def get_proxy(skip_rotate: bool = False):
	return get_iproyal_residential()
	# return get_iproyal_mobile(skip_rotate=skip_rotate)